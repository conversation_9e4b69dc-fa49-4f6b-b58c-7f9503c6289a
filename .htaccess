# Remove index.php and public from URLs
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # If the requested path doesn't contain a period and isn't a directory,
    # redirect to index.php in the public subdirectory
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ public/$1 [L]
    
    # If the requested path is the root, redirect to the public subdirectory
    RewriteRule ^$ public/ [L]
</IfModule>

# Prevent directory listing
Options -Indexes
