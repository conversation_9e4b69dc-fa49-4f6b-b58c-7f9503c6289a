<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #4285f4;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .btn-primary {
            background-color: #4285f4;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3367d6;
        }
        
        .email-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .email-table th,
        .email-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .email-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .email-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-sending {
            background-color: #cce7ff;
            color: #004085;
        }
        
        .subject-cell {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .recipients-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .date-cell {
            white-space: nowrap;
        }
        
        .success-rate {
            font-weight: bold;
        }
        
        .success-rate.high {
            color: #28a745;
        }
        
        .success-rate.medium {
            color: #ffc107;
        }
        
        .success-rate.low {
            color: #dc3545;
        }
        
        .no-emails {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-emails i {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }
        
        .pagination {
            text-align: center;
            margin-top: 20px;
        }
        
        .pagination a {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 4px;
            text-decoration: none;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #4285f4;
        }
        
        .pagination a:hover {
            background-color: #f8f9fa;
        }
        
        .pagination .current {
            background-color: #4285f4;
            color: white;
            border-color: #4285f4;
        }
        
        @media (max-width: 768px) {
            .email-table {
                font-size: 0.9em;
            }
            
            .subject-cell {
                max-width: 150px;
            }
            
            .recipients-cell {
                max-width: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Email History</h1>
        
        <!-- Statistics Summary -->
        <div class="stats-summary">
            <div class="stat-item">
                <div class="stat-number"><?= $stats['total_emails'] ?></div>
                <div class="stat-label">Total Emails</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $stats['successful_emails'] ?></div>
                <div class="stat-label">Successful</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $stats['failed_emails'] ?></div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $stats['pending_emails'] ?></div>
                <div class="stat-label">Pending</div>
            </div>
        </div>
        
        <a href="<?= base_url('email-sender') ?>" class="btn btn-primary">← Back to Dashboard</a>
        
        <?php if (!empty($emails)): ?>
        <table class="email-table">
            <thead>
                <tr>
                    <th>Subject</th>
                    <th>Recipients</th>
                    <th>Status</th>
                    <th>Success Rate</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($emails as $email): ?>
                <tr>
                    <td class="subject-cell" title="<?= esc($email['subject']) ?>">
                        <?= esc($email['subject']) ?>
                    </td>
                    <td class="recipients-cell">
                        <?= $email['total_recipients'] ?> recipients
                    </td>
                    <td>
                        <span class="status-badge status-<?= $email['status'] ?>">
                            <?= ucfirst($email['status']) ?>
                        </span>
                    </td>
                    <td>
                        <?php if ($email['status'] === 'completed' || $email['status'] === 'failed'): ?>
                            <?php 
                            $successRate = $email['total_recipients'] > 0 ? 
                                round(($email['successful_sends'] / $email['total_recipients']) * 100) : 0;
                            $rateClass = $successRate >= 80 ? 'high' : ($successRate >= 50 ? 'medium' : 'low');
                            ?>
                            <span class="success-rate <?= $rateClass ?>">
                                <?= $email['successful_sends'] ?>/<?= $email['total_recipients'] ?> (<?= $successRate ?>%)
                            </span>
                        <?php else: ?>
                            <span class="success-rate">-</span>
                        <?php endif; ?>
                    </td>
                    <td class="date-cell">
                        <?= date('M j, Y', strtotime($email['created_at'])) ?><br>
                        <small><?= date('g:i A', strtotime($email['created_at'])) ?></small>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <!-- Pagination would go here if implemented -->
        
        <?php else: ?>
        <div class="no-emails">
            <i>📧</i>
            <h3>No emails sent yet</h3>
            <p>Start by composing your first email!</p>
            <a href="<?= base_url('email-sender/new') ?>" class="btn btn-primary">Compose Email</a>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
