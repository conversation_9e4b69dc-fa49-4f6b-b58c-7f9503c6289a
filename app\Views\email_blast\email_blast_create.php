<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="email"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
            font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>a, Geneva, Verdana, sans-serif;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        input[type="text"]:focus, input[type="email"]:focus, textarea:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }
        
        textarea {
            resize: vertical;
            line-height: 1.6;
        }
        
        .recipients-input {
            height: 120px;
            font-family: monospace;
            line-height: 1.6;
        }
        
        .message-input {
            height: 250px;
            line-height: 1.6;
        }
        
        button {
            background-color: #4285f4;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: #3367d6;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 10px;
            width: auto;
            padding: 12px 24px;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 8px;
            line-height: 1.4;
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #4285f4;
        }
        
        .setup-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .setup-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .reply-to-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .reply-to-section h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .error-list {
            margin: 0;
            padding-left: 20px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✉️ Compose Email Blast</h1>
        
        <div class="setup-info">
            <h3>📋 Instructions:</h3>
            <ul>
                <li>Enter recipients in the format: Name, <EMAIL> (one per line)</li>
                <li>Use {{name}} in your message to personalize with recipient's name</li>
                <li>Specify a reply-to email address for responses</li>
                <li>Emails are sent with a 1-second delay between each recipient</li>
                <li>All emails will be sent using the configured SMTP settings</li>
            </ul>
        </div>
        
        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-error">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>
        
        <?php if (session()->getFlashdata('errors')): ?>
            <div class="alert alert-error">
                <strong>Please fix the following errors:</strong>
                <ul class="error-list">
                    <?php foreach (session()->getFlashdata('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form action="<?= base_url('email-blast/create') ?>" method="post">
            <?= csrf_field() ?>
        
        <!-- Reply-to Email Configuration -->
        <div class="reply-to-section">
            <h3>📧 Reply-to Email Configuration</h3>
            <div class="form-group">
                <label for="reply_to_email">Reply-to Email Address:</label>
                <input type="email" id="reply_to_email" name="reply_to_email" value="<?= old('reply_to_email', '<EMAIL>') ?>" placeholder="<EMAIL>">
                <div class="help-text">When recipients reply to the email, their responses will be sent to this address. Leave blank to use the default (<EMAIL>)</div>
            </div>
        </div>
        
        <!-- Email Content -->
        <div class="form-group">
            <label for="subject">Email Subject: <span class="required">*</span></label>
            <input type="text" id="subject" name="subject" value="<?= old('subject') ?>" required>
        </div>
        
        <div class="form-group">
            <label for="recipients">Recipients: <span class="required">*</span></label>
            <textarea id="recipients" name="recipients" class="recipients-input" required placeholder="John Doe, <EMAIL>&#10;Jane Smith, <EMAIL>&#10;Bob Johnson, <EMAIL>"><?= old('recipients') ?></textarea>
            <div class="help-text">Format: Name, <EMAIL> (one per line)</div>
        </div>
        
        <div class="form-group">
            <label for="message">Message: <span class="required">*</span></label>
            <textarea id="message" name="message" class="message-input" required placeholder="Hello {{name}},&#10;&#10;Your personalized message here...&#10;&#10;Best regards"><?= old('message') ?></textarea>
            <div class="help-text">Use {{name}} to personalize with recipient's name. Line breaks will be preserved in the email.</div>
        </div>
        
        <div class="button-group">
            <a href="<?= base_url('email-blast') ?>" class="btn-secondary">← Back to Dashboard</a>
            <button type="submit">Send Email Blast</button>
        </div>
        
        </form>
    </div>
</body>
</html>
