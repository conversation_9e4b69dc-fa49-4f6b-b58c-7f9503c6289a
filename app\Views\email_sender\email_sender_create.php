<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="email"], input[type="password"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
        }
        
        .recipients-input {
            height: 100px;
        }
        
        .message-input {
            height: 200px;
        }
        
        button {
            background-color: #4285f4;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: #3367d6;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 10px;
            width: auto;
            padding: 12px 24px;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .setup-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .setup-info h3 {
            margin-top: 0;
            color: #856404;
        }
        
        .smtp-config {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .smtp-config h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .error-list {
            margin: 0;
            padding-left: 20px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✉️ Compose Email</h1>
        
        <div class="setup-info">
            <h3>📋 Instructions:</h3>
            <ul>
                <li>Enter recipients in the format: Name, <EMAIL> (one per line)</li>
                <li>Use {{name}} in your message to personalize with recipient's name</li>
                <li>Configure SMTP settings below or use default email configuration</li>
                <li>Emails are sent with a 1-second delay between each recipient</li>
            </ul>
        </div>
        
        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-error">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>
        
        <?php if (session()->getFlashdata('errors')): ?>
            <div class="alert alert-error">
                <strong>Please fix the following errors:</strong>
                <ul class="error-list">
                    <?php foreach (session()->getFlashdata('errors') as $error): ?>
                        <li><?= esc($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form action="<?= base_url('email-sender/create') ?>" method="post">
            <?= csrf_field() ?>

        <!-- SMTP Configuration (Optional) -->
        <div class="smtp-config">
            <h3>🔧 SMTP Configuration (Optional)</h3>
            <p class="help-text">Leave blank to use default email configuration from Config/Email.php</p>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="smtp_host">SMTP Host:</label>
                    <input type="text" id="smtp_host" name="smtp_host" value="<?= old('smtp_host') ?>" placeholder="smtp.gmail.com">
                </div>
                <div class="form-group">
                    <label for="smtp_port">SMTP Port:</label>
                    <input type="number" id="smtp_port" name="smtp_port" value="<?= old('smtp_port') ?>" placeholder="587">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="smtp_user">SMTP Username:</label>
                    <input type="email" id="smtp_user" name="smtp_user" value="<?= old('smtp_user') ?>" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="smtp_pass">SMTP Password:</label>
                    <input type="password" id="smtp_pass" name="smtp_pass" placeholder="Your app password">
                    <div class="help-text">For Gmail, use an App Password, not your regular password</div>
                </div>
            </div>
        </div>
        
        <!-- Email Content -->
        <div class="form-group">
            <label for="subject">Email Subject:</label>
            <input type="text" id="subject" name="subject" value="<?= old('subject') ?>" required>
        </div>
        
        <div class="form-group">
            <label for="recipients">Recipients:</label>
            <textarea id="recipients" name="recipients" class="recipients-input" required placeholder="John Doe, <EMAIL>&#10;Jane Smith, <EMAIL>&#10;Bob Johnson, <EMAIL>"><?= old('recipients') ?></textarea>
            <div class="help-text">Format: Name, <EMAIL> (one per line)</div>
        </div>
        
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" name="message" class="message-input" required placeholder="Hello {{name}},&#10;&#10;Your personalized message here...&#10;&#10;Best regards"><?= old('message') ?></textarea>
            <div class="help-text">Use {{name}} to personalize with recipient's name</div>
        </div>
        
        <div class="button-group">
            <a href="<?= base_url('email-sender') ?>" class="btn-secondary">← Back to Dashboard</a>
            <button type="submit">Send Emails</button>
        </div>
        
        </form>
    </div>
</body>
</html>
