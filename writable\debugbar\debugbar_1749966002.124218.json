{"url": "http://localhost/codei46/email-sender/new", "method": "GET", "isAJAX": false, "startTime": **********.66165, "totalTime": 205.2, "totalMemory": "4.819", "segmentDuration": 30, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.682023, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.768645, "duration": 0.012649059295654297}, {"name": "Routing", "component": "Timer", "start": **********.781305, "duration": 0.0024738311767578125}, {"name": "Before Filters", "component": "Timer", "start": **********.785505, "duration": 2.8848648071289062e-05}, {"name": "Controller", "component": "Timer", "start": **********.785536, "duration": 0.****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.785537, "duration": 0.020968914031982422}, {"name": "After Filters", "component": "Timer", "start": **********.864556, "duration": 8.821487426757812e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.864613, "duration": 0.0022928714752197266}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(0 total Query, 0  unique across 0 Connection)", "display": {"queries": []}, "badgeValue": 0, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 1, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: email_sender/email_sender_create.php", "component": "Views", "start": **********.829721, "duration": 0.03434109687805176}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 149 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Email\\Email.php", "name": "Email.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\Array\\ArrayHelper.php", "name": "ArrayHelper.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Security\\Security.php", "name": "Security.php"}, {"path": "SYSTEMPATH\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Validation\\CreditCardRules.php", "name": "CreditCardRules.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\Validation\\Rules.php", "name": "Rules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\CreditCardRules.php", "name": "CreditCardRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\FileRules.php", "name": "FileRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\Validation\\StrictRules\\Rules.php", "name": "Rules.php"}, {"path": "SYSTEMPATH\\Validation\\Validation.php", "name": "Validation.php"}, {"path": "SYSTEMPATH\\Validation\\ValidationInterface.php", "name": "ValidationInterface.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\DocTypes.php", "name": "DocTypes.php"}, {"path": "APPPATH\\Config\\Email.php", "name": "Email.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Security.php", "name": "Security.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\Validation.php", "name": "Validation.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\EmailSender.php", "name": "EmailSender.php"}, {"path": "APPPATH\\Views\\email_sender\\email_sender_create.php", "name": "email_sender_create.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 149, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\EmailSender", "method": "new", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "email-sender", "handler": "\\App\\Controllers\\EmailSender::index"}, {"method": "GET", "route": "email-sender/new", "handler": "\\App\\Controllers\\EmailSender::new"}, {"method": "GET", "route": "email-sender/history", "handler": "\\App\\Controllers\\EmailSender::history"}, {"method": "POST", "route": "email-sender/create", "handler": "\\App\\Controllers\\EmailSender::create"}]}, "badgeValue": 5, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "36.82", "count": 1}}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.731793, "duration": 0.0368189811706543}]}], "vars": {"varData": {"View Data": {"title": "Compose Email", "validation": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>CodeIgniter\\Validation\\Validation</var>#81 (9)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (9)</li><li>Methods (29)</li><li>Static methods (1)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>ruleSetFiles</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (40) \"CodeIgniter\\Validation\\StrictRules\\Rules\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (46) \"CodeIgniter\\Validation\\StrictRules\\FormatRules\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (44) \"CodeIgniter\\Validation\\StrictRules\\FileRules\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (50) \"CodeIgniter\\Validation\\StrictRules\\CreditCardRules\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>ruleSetInstances</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>CodeIgniter\\Validation\\StrictRules\\Rules</var>#84 (1)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (1)</li><li>Methods (20)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>nonStrictRules</dfn> -&gt; <var>CodeIgniter\\Validation\\Rules</var>#85</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Methods (20)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>differs($str, string $field, array $data)</dfn>: <var>bool</var> The value does not match another field in $data.</dt><dd><pre>/**\n * The value does not match another field in $data.\n *\n * @param string|null $str\n * @param array       $data Other field/value pairs\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:34</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>equals($str, string $val)</dfn>: <var>bool</var> Equals the static value provided.</dt><dd><pre>/**\n * Equals the static value provided.\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:48</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>exact_length($str, string $val)</dfn>: <var>bool</var> Returns true if $str is $val characters long. $val = \"5\" (one) | \"5,8,12\" (mu...</dt><dd><pre>/**\n * Returns true if $str is $val characters long.\n * $val = \"5\" (one) | \"5,8,12\" (multiple values)\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:63</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>greater_than($str, string $min)</dfn>: <var>bool</var> Greater than</dt><dd><pre>/**\n * Greater than\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:85</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>greater_than_equal_to($str, string $min)</dfn>: <var>bool</var> Equal to or Greater than</dt><dd><pre>/**\n * Equal to or Greater than\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:99</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_not_unique($str, string $field, array $data)</dfn>: <var>bool</var> Checks the database to see if the given value exist. Can ignore records by fi...</dt><dd><pre>/**\n * Checks the database to see if the given value exist.\n * Can ignore records by field/value to filter (currently\n * accept only one filter).\n *\n * Example:\n *    is_not_unique[dbGroup.table.field,where_field,where_value]\n *    is_not_unique[table.field,where_field,where_value]\n *    is_not_unique[menu.id,active,1]\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:120</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>in_list($value, string $list)</dfn>: <var>bool</var> Value should be within an array of values</dt><dd><pre>/**\n * Value should be within an array of values\n *\n * @param string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:140</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_unique($str, string $field, array $data)</dfn>: <var>bool</var> Checks the database to see if the given value is unique. Can ignore a single ...</dt><dd><pre>/**\n * Checks the database to see if the given value is unique. Can\n * ignore a single record by field/value to make it useful during\n * record updates.\n *\n * Example:\n *    is_unique[dbGroup.table.field,ignore_field,ignore_value]\n *    is_unique[table.field,ignore_field,ignore_value]\n *    is_unique[users.email,id,5]\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:163</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>prepareUniqueQuery($value, string $field, array $data)</dfn>: <var>array</var> Prepares the database query for uniqueness checks.</dt><dd><pre>/**\n * Prepares the database query for uniqueness checks.\n *\n * @param mixed                $value The value to check.\n * @param string               $field The field parameters.\n * @param array&lt;string, mixed&gt; $data  Additional data.\n *\n * @return array{0: BaseBuilder, 1: string|null, 2: string|null}\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:187</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>less_than($str, string $max)</dfn>: <var>bool</var> Less than</dt><dd><pre>/**\n * Less than\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>less_than_equal_to($str, string $max)</dfn>: <var>bool</var> Equal to or Less than</dt><dd><pre>/**\n * Equal to or Less than\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:238</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>matches($str, string $field, array $data)</dfn>: <var>bool</var> Matches the value of another field in $data.</dt><dd><pre>/**\n * Matches the value of another field in $data.\n *\n * @param string|null $str\n * @param array       $data Other field/value pairs\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:253</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>max_length($str, string $val)</dfn>: <var>bool</var> Returns true if $str is $val or fewer characters in length.</dt><dd><pre>/**\n * Returns true if $str is $val or fewer characters in length.\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:267</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>min_length($str, string $val)</dfn>: <var>bool</var> Returns true if $str is at least $val length.</dt><dd><pre>/**\n * Returns true if $str is at least $val length.\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:281</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>not_equals($str, string $val)</dfn>: <var>bool</var> Does not equal the static value provided.</dt><dd><pre>/**\n * Does not equal the static value provided.\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:295</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>not_in_list($value, string $list)</dfn>: <var>bool</var> Value should not be within an array of values.</dt><dd><pre>/**\n * Value should not be within an array of values.\n *\n * @param string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:309</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>required($str = null)</dfn>: <var>bool</var></dt><dd><pre>/**\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:321</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>required_with($str = null, ?string $fields = null, array $data = array())</dfn>: <var>bool</var> The field is required when any of the other required fields are present in th...</dt><dd><pre>/**\n * The field is required when any of the other required fields are present\n * in the data.\n *\n * Example (field is required when the password field is present):\n *\n *     required_with[password]\n *\n * @param string|null $str\n * @param string|null $fields List of fields that we should check if present\n * @param array       $data   Complete list of fields from the form\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:350</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>required_without($str = null, ?string $otherFields = null, array $data = array(), ?string $error = null, ?string $field = null)</dfn>: <var>bool</var> The field is required when all the other fields are present in the data but n...</dt><dd><pre>/**\n * The field is required when all the other fields are present\n * in the data but not required.\n *\n * Example (field is required when the id or email field is missing):\n *\n *     required_without[id,email]\n *\n * @param string|null $str\n * @param string|null $otherFields The param fields of required_without[].\n * @param string|null $field       This rule param fields aren't present, this field is required.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:394</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>field_exists($value = null, ?string $param = null, array $data = array(), ?string $error = null, ?string $field = null)</dfn>: <var>bool</var> The field exists in $data.</dt><dd><pre>/**\n * The field exists in $data.\n *\n * @param array|bool|float|int|object|string|null $value The field value.\n * @param string|null                             $param The rule's parameter.\n * @param array                                   $data  The data to be validated.\n * @param string|null                             $field The field name.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Rules.php:455</small></pre></dd></dl></li></ul></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:28</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>differs($str, string $otherField, array $data, ?string $error = null, ?string $field = null)</dfn>: <var>bool</var> The value does not match another field in $data.</dt><dd><pre>/**\n * The value does not match another field in $data.\n *\n * @param array|bool|float|int|object|string|null $str\n * @param array                                   $data Other field/value pairs\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:39</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>equals($str, string $val)</dfn>: <var>bool</var> Equals the static value provided.</dt><dd><pre>/**\n * Equals the static value provided.\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:70</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>exact_length($str, string $val)</dfn>: <var>bool</var> Returns true if $str is $val characters long. $val = \"5\" (one) | \"5,8,12\" (mu...</dt><dd><pre>/**\n * Returns true if $str is $val characters long.\n * $val = \"5\" (one) | \"5,8,12\" (multiple values)\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:81</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>greater_than($str, string $min)</dfn>: <var>bool</var> Greater than</dt><dd><pre>/**\n * Greater than\n *\n * @param array|bool|float|int|object|string|null $str expects int|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:99</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>greater_than_equal_to($str, string $min)</dfn>: <var>bool</var> Equal to or Greater than</dt><dd><pre>/**\n * Equal to or Greater than\n *\n * @param array|bool|float|int|object|string|null $str expects int|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:117</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_not_unique($str, string $field, array $data)</dfn>: <var>bool</var> Checks the database to see if the given value exist. Can ignore records by fi...</dt><dd><pre>/**\n * Checks the database to see if the given value exist.\n * Can ignore records by field/value to filter (currently\n * accept only one filter).\n *\n * Example:\n *    is_not_unique[dbGroup.table.field,where_field,where_value]\n *    is_not_unique[table.field,where_field,where_value]\n *    is_not_unique[menu.id,active,1]\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:142</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>in_list($value, string $list)</dfn>: <var>bool</var> Value should be within an array of values</dt><dd><pre>/**\n * Value should be within an array of values\n *\n * @param array|bool|float|int|object|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:156</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_unique($str, string $field, array $data)</dfn>: <var>bool</var> Checks the database to see if the given value is unique. Can ignore a single ...</dt><dd><pre>/**\n * Checks the database to see if the given value is unique. Can\n * ignore a single record by field/value to make it useful during\n * record updates.\n *\n * Example:\n *    is_unique[dbGroup.table.field,ignore_field,ignore_value]\n *    is_unique[table.field,ignore_field,ignore_value]\n *    is_unique[users.email,id,5]\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>less_than($str, string $max)</dfn>: <var>bool</var> Less than</dt><dd><pre>/**\n * Less than\n *\n * @param array|bool|float|int|object|string|null $str expects int|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:195</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>less_than_equal_to($str, string $max)</dfn>: <var>bool</var> Equal to or Less than</dt><dd><pre>/**\n * Equal to or Less than\n *\n * @param array|bool|float|int|object|string|null $str expects int|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:213</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>matches($str, string $otherField, array $data, ?string $error = null, ?string $field = null)</dfn>: <var>bool</var> Matches the value of another field in $data.</dt><dd><pre>/**\n * Matches the value of another field in $data.\n *\n * @param array|bool|float|int|object|string|null $str\n * @param array                                   $data Other field/value pairs\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:232</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>max_length($str, string $val)</dfn>: <var>bool</var> Returns true if $str is $val or fewer characters in length.</dt><dd><pre>/**\n * Returns true if $str is $val or fewer characters in length.\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:263</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>min_length($str, string $val)</dfn>: <var>bool</var> Returns true if $str is at least $val length.</dt><dd><pre>/**\n * Returns true if $str is at least $val length.\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:281</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>not_equals($str, string $val)</dfn>: <var>bool</var> Does not equal the static value provided.</dt><dd><pre>/**\n * Does not equal the static value provided.\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:299</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>not_in_list($value, string $list)</dfn>: <var>bool</var> Value should not be within an array of values.</dt><dd><pre>/**\n * Value should not be within an array of values.\n *\n * @param array|bool|float|int|object|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:309</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>required($str = null)</dfn>: <var>bool</var></dt><dd><pre>/**\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:329</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>required_with($str = null, ?string $fields = null, array $data = array())</dfn>: <var>bool</var> The field is required when any of the other required fields are present in th...</dt><dd><pre>/**\n * The field is required when any of the other required fields are present\n * in the data.\n *\n * Example (field is required when the password field is present):\n *\n *     required_with[password]\n *\n * @param array|bool|float|int|object|string|null $str\n * @param string|null                             $fields List of fields that we should check if present\n * @param array                                   $data   Complete list of fields from the form\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:346</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>required_without($str = null, ?string $otherFields = null, array $data = array(), ?string $error = null, ?string $field = null)</dfn>: <var>bool</var> The field is required when all the other fields are present in the data but n...</dt><dd><pre>/**\n * The field is required when all the other fields are present\n * in the data but not required.\n *\n * Example (field is required when the id or email field is missing):\n *\n *     required_without[id,email]\n *\n * @param array|bool|float|int|object|string|null $str\n * @param string|null                             $otherFields The param fields of required_without[].\n * @param string|null                             $field       This rule param fields aren't present, this field is required.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:363</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>field_exists($value = null, ?string $param = null, array $data = array(), ?string $error = null, ?string $field = null)</dfn>: <var>bool</var> The field exists in $data.</dt><dd><pre>/**\n * The field exists in $data.\n *\n * @param array|bool|float|int|object|string|null $value The field value.\n * @param string|null                             $param The rule's parameter.\n * @param array                                   $data  The data to be validated.\n * @param string|null                             $field The field name.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/Rules.php:381</small></pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>1</dfn> =&gt; <var>CodeIgniter\\Validation\\StrictRules\\FormatRules</var>#86 (1)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (1)</li><li>Methods (24)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>nonStrictFormatRules</dfn> -&gt; <var>CodeIgniter\\Validation\\FormatRules</var>#87</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Methods (23)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha($str = null)</dfn>: <var>bool</var> Alpha</dt><dd><pre>/**\n * Alpha\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:30</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_space($value = null)</dfn>: <var>bool</var> Alpha with spaces.</dt><dd><pre>/**\n * Alpha with spaces.\n *\n * @param string|null $value Value.\n *\n * @return bool True if alpha with spaces, else false.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:46</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_dash($str = null)</dfn>: <var>bool</var> Alphanumeric with underscores and dashes</dt><dd><pre>/**\n * Alphanumeric with underscores and dashes\n *\n * @see https://regex101.com/r/XfVY3d/1\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:67</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_numeric_punct($str)</dfn>: <var>bool</var> Alphanumeric, spaces, and a limited set of punctuation characters. Accepted p...</dt><dd><pre>/**\n * Alphanumeric, spaces, and a limited set of punctuation characters.\n * Accepted punctuation characters are: ~ tilde, ! exclamation,\n * # number, $ dollar, % percent, &amp; ampersand, * asterisk, - dash,\n * _ underscore, + plus, = equals, | vertical bar, : colon, . period\n * ~ ! # $ % &amp; * - _ + = | : .\n *\n * @param string|null $str\n *\n * @return bool\n *\n * @see https://regex101.com/r/6N8dDY/1\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:93</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_numeric($str = null)</dfn>: <var>bool</var> Alphanumeric</dt><dd><pre>/**\n * Alphanumeric\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:111</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_numeric_space($str = null)</dfn>: <var>bool</var> Alphanumeric w/ spaces</dt><dd><pre>/**\n * Alphanumeric w/ spaces\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:125</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>string($str = null)</dfn>: <var>bool</var> Any type of string</dt><dd><pre>/**\n * Any type of string\n *\n * Note: we specifically do NOT type hint $str here so that\n * it doesn't convert numbers into strings.\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>decimal($str = null)</dfn>: <var>bool</var> Decimal number</dt><dd><pre>/**\n * Decimal number\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:153</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>hex($str = null)</dfn>: <var>bool</var> String of hexidecimal characters</dt><dd><pre>/**\n * String of hexidecimal characters\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:168</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>integer($str = null)</dfn>: <var>bool</var> Integer</dt><dd><pre>/**\n * Integer\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:182</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_natural($str = null)</dfn>: <var>bool</var> Is a Natural number  (0,1,2,3, etc.)</dt><dd><pre>/**\n * Is a Natural number  (0,1,2,3, etc.)\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:196</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_natural_no_zero($str = null)</dfn>: <var>bool</var> Is a Natural number, but not a zero  (1,2,3, etc.)</dt><dd><pre>/**\n * Is a Natural number, but not a zero  (1,2,3, etc.)\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>numeric($str = null)</dfn>: <var>bool</var> Numeric</dt><dd><pre>/**\n * Numeric\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>regex_match($str, string $pattern)</dfn>: <var>bool</var> Compares value against a regular expression pattern.</dt><dd><pre>/**\n * Compares value against a regular expression pattern.\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:239</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>timezone($str = null)</dfn>: <var>bool</var> Validates that the string is a valid timezone as per the timezone_identifiers...</dt><dd><pre>/**\n * Validates that the string is a valid timezone as per the\n * timezone_identifiers_list function.\n *\n * @see http://php.net/manual/en/datetimezone.listidentifiers.php\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:260</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_base64($str = null)</dfn>: <var>bool</var> Valid Base64</dt><dd><pre>/**\n * Valid Base64\n *\n * Tests a string for characters outside of the Base64 alphabet\n * as defined by RFC 2045 http://www.faqs.org/rfcs/rfc2045\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:277</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_json($str = null)</dfn>: <var>bool</var> Valid JSON</dt><dd><pre>/**\n * Valid JSON\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:295</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_email($str = null)</dfn>: <var>bool</var> Checks for a correctly formatted email address</dt><dd><pre>/**\n * Checks for a correctly formatted email address\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:311</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_emails($str = null)</dfn>: <var>bool</var> Validate a comma-separated list of email addresses.</dt><dd><pre>/**\n * Validate a comma-separated list of email addresses.\n *\n * Example:\n *     valid_emails[<EMAIL>,<EMAIL>]\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:333</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_ip($ip = null, ?string $which = null)</dfn>: <var>bool</var> Validate an IP address (human readable format or binary string - inet_pton)</dt><dd><pre>/**\n * Validate an IP address (human readable format or binary string - inet_pton)\n *\n * @param string|null $ip\n * @param string|null $which IP protocol: 'ipv4' or 'ipv6'\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_url($str = null)</dfn>: <var>bool</var> Checks a string to ensure it is (loosely) a URL.</dt><dd><pre>/**\n * Checks a string to ensure it is (loosely) a URL.\n *\n * Warning: this rule will pass basic strings like\n * \"banana\"; use valid_url_strict for a stricter rule.\n *\n * @param string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:388</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_url_strict($str = null, ?string $validSchemes = null)</dfn>: <var>bool</var> Checks a URL to ensure it's formed correctly.</dt><dd><pre>/**\n * Checks a URL to ensure it's formed correctly.\n *\n * @param string|null $str\n * @param string|null $validSchemes comma separated list of allowed schemes\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:417</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_date($str = null, ?string $format = null)</dfn>: <var>bool</var> Checks for a valid date and matches a given date format</dt><dd><pre>/**\n * Checks for a valid date and matches a given date format\n *\n * @param string|null           $str\n * @param non-empty-string|null $format\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/FormatRules.php:444</small></pre></dd></dl></li></ul></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:27</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha($str = null)</dfn>: <var>bool</var> Alpha</dt><dd><pre>/**\n * Alpha\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:37</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_space($value = null)</dfn>: <var>bool</var> Alpha with spaces.</dt><dd><pre>/**\n * Alpha with spaces.\n *\n * @param array|bool|float|int|object|string|null $value Value.\n *\n * @return bool True if alpha with spaces, else false.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:53</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_dash($str = null)</dfn>: <var>bool</var> Alphanumeric with underscores and dashes</dt><dd><pre>/**\n * Alphanumeric with underscores and dashes\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:67</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_numeric_punct($str)</dfn>: <var>bool</var> Alphanumeric, spaces, and a limited set of punctuation characters. Accepted p...</dt><dd><pre>/**\n * Alphanumeric, spaces, and a limited set of punctuation characters.\n * Accepted punctuation characters are: ~ tilde, ! exclamation,\n * # number, $ dollar, % percent, &amp; ampersand, * asterisk, - dash,\n * _ underscore, + plus, = equals, | vertical bar, : colon, . period\n * ~ ! # $ % &amp; * - _ + = | : .\n *\n * @param array|bool|float|int|object|string|null $str\n *\n * @return bool\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_numeric($str = null)</dfn>: <var>bool</var> Alphanumeric</dt><dd><pre>/**\n * Alphanumeric\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:109</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alpha_numeric_space($str = null)</dfn>: <var>bool</var> Alphanumeric w/ spaces</dt><dd><pre>/**\n * Alphanumeric w/ spaces\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>string($str = null)</dfn>: <var>bool</var> Any type of string</dt><dd><pre>/**\n * Any type of string\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:145</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>decimal($str = null)</dfn>: <var>bool</var> Decimal number</dt><dd><pre>/**\n * Decimal number\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>hex($str = null)</dfn>: <var>bool</var> String of hexidecimal characters</dt><dd><pre>/**\n * String of hexidecimal characters\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:173</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>integer($str = null)</dfn>: <var>bool</var> Integer</dt><dd><pre>/**\n * Integer\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:191</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_natural($str = null)</dfn>: <var>bool</var> Is a Natural number  (0,1,2,3, etc.)</dt><dd><pre>/**\n * Is a Natural number  (0,1,2,3, etc.)\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:209</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_natural_no_zero($str = null)</dfn>: <var>bool</var> Is a Natural number, but not a zero  (1,2,3, etc.)</dt><dd><pre>/**\n * Is a Natural number, but not a zero  (1,2,3, etc.)\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:227</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>numeric($str = null)</dfn>: <var>bool</var> Numeric</dt><dd><pre>/**\n * Numeric\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:245</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>regex_match($str, string $pattern)</dfn>: <var>bool</var> Compares value against a regular expression pattern.</dt><dd><pre>/**\n * Compares value against a regular expression pattern.\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:263</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>timezone($str = null)</dfn>: <var>bool</var> Validates that the string is a valid timezone as per the timezone_identifiers...</dt><dd><pre>/**\n * Validates that the string is a valid timezone as per the\n * timezone_identifiers_list function.\n *\n * @see http://php.net/manual/en/datetimezone.listidentifiers.php\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:280</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_base64($str = null)</dfn>: <var>bool</var> Valid Base64</dt><dd><pre>/**\n * Valid Base64\n *\n * Tests a string for characters outside of the Base64 alphabet\n * as defined by RFC 2045 http://www.faqs.org/rfcs/rfc2045\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:297</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_json($str = null)</dfn>: <var>bool</var> Valid JSON</dt><dd><pre>/**\n * Valid JSON\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:311</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_email($str = null)</dfn>: <var>bool</var> Checks for a correctly formatted email address</dt><dd><pre>/**\n * Checks for a correctly formatted email address\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:325</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_emails($str = null)</dfn>: <var>bool</var> Validate a comma-separated list of email addresses.</dt><dd><pre>/**\n * Validate a comma-separated list of email addresses.\n *\n * Example:\n *     valid_emails[<EMAIL>,<EMAIL>]\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:342</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_ip($ip = null, ?string $which = null)</dfn>: <var>bool</var> Validate an IP address (human readable format or binary string - inet_pton)</dt><dd><pre>/**\n * Validate an IP address (human readable format or binary string - inet_pton)\n *\n * @param array|bool|float|int|object|string|null $ip\n * @param string|null                             $which IP protocol: 'ipv4' or 'ipv6'\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:357</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_url($str = null)</dfn>: <var>bool</var> Checks a string to ensure it is (loosely) a URL.</dt><dd><pre>/**\n * Checks a string to ensure it is (loosely) a URL.\n *\n * Warning: this rule will pass basic strings like\n * \"banana\"; use valid_url_strict for a stricter rule.\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:374</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_url_strict($str = null, ?string $validSchemes = null)</dfn>: <var>bool</var> Checks a URL to ensure it's formed correctly.</dt><dd><pre>/**\n * Checks a URL to ensure it's formed correctly.\n *\n * @param array|bool|float|int|object|string|null $str\n * @param string|null                             $validSchemes comma separated list of allowed schemes\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:389</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_date($str = null, ?string $format = null)</dfn>: <var>bool</var> Checks for a valid date and matches a given date format</dt><dd><pre>/**\n * Checks for a valid date and matches a given date format\n *\n * @param array|bool|float|int|object|string|null $str\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FormatRules.php:403</small></pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>2</dfn> =&gt; <var>CodeIgniter\\Validation\\StrictRules\\FileRules</var>#88 (1)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (1)</li><li>Methods (8)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>request</dfn> -&gt; <var>CodeIgniter\\HTTP\\IncomingRequest</var>#20 (18)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (18)</li><li>Methods (67)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>protocolVersion</dfn> -&gt; <var>string</var> (3) \"1.1\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>validProtocolVersions</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (3) \"1.0\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (3) \"1.1\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (3) \"2.0\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (3) \"3.0\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>body</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>headers</dfn> -&gt; <var>array</var> (17)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>Host</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#27 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (4) \"Host\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Host</dfn> <var>string</var> (15) \"Host: localhost\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Connection</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#28 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (10) \"Connection\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (10) \"keep-alive\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Connection</dfn> <var>string</var> (22) \"Connection: keep-alive\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Cache-Control</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#29 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (13) \"Cache-Control\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (9) \"max-age=0\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Cache-Control</dfn> <var>string</var> (24) \"Cache-Control: max-age=0\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Sec-Ch-Ua</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#30 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (9) \"Sec-Ch-Ua\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (65) \"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Sec-Ch-Ua</dfn> <var>string</var> (76) \"Sec-Ch-Ua: \"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Sec-Ch-Ua-Mobile</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#31 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (16) \"Sec-Ch-Ua-Mobile\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (2) \"?0\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Sec-Ch-Ua-Mobile</dfn> <var>string</var> (20) \"Sec-Ch-Ua-Mobile: ?0\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Sec-Ch-Ua-Platform</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#32 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (18) \"Sec-Ch-Ua-Platform\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (9) \"\"Windows\"\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Sec-Ch-Ua-Platform</dfn> <var>string</var> (29) \"Sec-Ch-Ua-Platform: \"Windows\"\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Upgrade-Insecure-Requests</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#33 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (25) \"Upgrade-Insecure-Requests\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (1) \"1\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Upgrade-Insecure-Requests</dfn> <var>string</var> (28) \"Upgrade-Insecure-Requests: 1\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>User-Agent</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#34 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (10) \"User-Agent\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (111) \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Ge...</dt><dd><pre>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n</pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><dfn>User-Agent</dfn> <var>string</var> (123) \"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KH...</dt><dd><pre>User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n</pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Accept</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#35 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (6) \"Accept\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (135) \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,...</dt><dd><pre>text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n</pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><dfn>Accept</dfn> <var>string</var> (143) \"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,ima...</dt><dd><pre>Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n</pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Sec-Fetch-Site</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#36 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (14) \"Sec-Fetch-Site\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (11) \"same-origin\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Sec-Fetch-Site</dfn> <var>string</var> (27) \"Sec-Fetch-Site: same-origin\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Sec-Fetch-Mode</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#37 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (14) \"Sec-Fetch-Mode\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (8) \"navigate\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Sec-Fetch-Mode</dfn> <var>string</var> (24) \"Sec-Fetch-Mode: navigate\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Sec-Fetch-User</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#38 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (14) \"Sec-Fetch-User\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (2) \"?1\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Sec-Fetch-User</dfn> <var>string</var> (18) \"Sec-Fetch-User: ?1\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Sec-Fetch-Dest</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#39 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (14) \"Sec-Fetch-Dest\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (8) \"document\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Sec-Fetch-Dest</dfn> <var>string</var> (24) \"Sec-Fetch-Dest: document\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Referer</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#40 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (7) \"Referer\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (37) \"http://localhost/codei46/email-sender\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Referer</dfn> <var>string</var> (46) \"Referer: http://localhost/codei46/email-sender\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Accept-Encoding</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#41 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (15) \"Accept-Encoding\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (23) \"gzip, deflate, br, zstd\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Accept-Encoding</dfn> <var>string</var> (40) \"Accept-Encoding: gzip, deflate, br, zstd\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Accept-Language</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#42 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (15) \"Accept-Language\"</dt></dl><dl><dt><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (14) \"en-US,en;q=0.9\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt><dfn>Accept-Language</dfn> <var>string</var> (31) \"Accept-Language: en-US,en;q=0.9\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Cookie</dfn> =&gt; <var>CodeIgniter\\HTTP\\Header</var>#43 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (11)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>name</dfn> -&gt; <var>string</var> (6) \"Cookie\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>value</dfn> -&gt; <var>string</var> (94) \"csrf_cookie_name=2dad20e4de25624936e6bca004bb1ba1; ci_session=e0977661fad7ae...</dt><dd><pre>csrf_cookie_name=2dad20e4de25624936e6bca004bb1ba1; ci_session=e0977661fad7aee083577aa7804c3fbf\n</pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(string $name, $value = null)</dfn> Header constructor. name is mandatory, if a value is provided, it will be set.</dt><dd><pre>/**\n * Header constructor. name is mandatory, if a value is provided, it will be set.\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:56</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getName()</dfn>: <var>string</var> Returns the name of the header, in the same case it was set.</dt><dd><pre>/**\n * Returns the name of the header, in the same case it was set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:65</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValue()</dfn>: <var>array&lt;int|string, array&lt;string, string&gt;|string&gt;|string</var> Gets the raw value of the header. This may return either a string or an array...</dt><dd><pre>/**\n * Gets the raw value of the header. This may return either a string\n * or an array, depending on whether the header has multiple values or not.\n *\n * @return array&lt;int|string, array&lt;string, string&gt;|string&gt;|string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:76</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setName(string $name)</dfn>: <var>$this</var> Sets the name of the header, overwriting any previous value.</dt><dd><pre>/**\n * Sets the name of the header, overwriting any previous value.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:88</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValue($value = null)</dfn>: <var>$this</var> Sets the value of the header, overwriting any previous value(s).</dt><dd><pre>/**\n * Sets the value of the header, overwriting any previous value(s).\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:105</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendValue($value = null)</dfn>: <var>$this</var> Appends a value to the list of values for this header. If the header is a sin...</dt><dd><pre>/**\n * Appends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:126</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependValue($value = null)</dfn>: <var>$this</var> Prepends a value to the list of values for this header. If the header is a si...</dt><dd><pre>/**\n * Prepends a value to the list of values for this header. If the\n * header is a single value string, it will be converted to an array.\n *\n * @param array&lt;string, string&gt;|string|null $value\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getValueLine()</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n *\n * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Returns a representation of the entire header string, including the header na...</dt><dd><pre>/**\n * Returns a representation of the entire header string, including\n * the header name and all values converted to the proper format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateName(string $name)</dfn>: <var>void</var> Validate header name.</dt><dd><pre>/**\n * Validate header name.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:224</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>validateValue(array|string|int $value)</dfn>: <var>void</var> Validate header value.</dt><dd><pre>/**\n * Validate header value.\n *\n * Regex is based on code from a guzzlehttp/psr7 library.\n *\n * @see https://datatracker.ietf.org/doc/html/rfc7230#section-3.2\n *\n * @param array&lt;int|string, array&lt;string, string&gt;|string&gt;|int|string $value\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/Header.php:242</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><dfn>Cookie</dfn> <var>string</var> (102) \"Cookie: csrf_cookie_name=2dad20e4de25624936e6bca004bb1ba1; ci_session=e09776...</dt><dd><pre>Cookie: csrf_cookie_name=2dad20e4de25624936e6bca004bb1ba1; ci_session=e0977661fad7aee083577aa7804c3fbf\n</pre></dd></dl></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>headerMap</dfn> -&gt; <var>array</var> (17)</dt><dd><dl><dt><dfn>host</dfn> =&gt; <var>string</var> (4) \"Host\"</dt></dl><dl><dt><dfn>connection</dfn> =&gt; <var>string</var> (10) \"Connection\"</dt></dl><dl><dt><dfn>cache-control</dfn> =&gt; <var>string</var> (13) \"Cache-Control\"</dt></dl><dl><dt><dfn>sec-ch-ua</dfn> =&gt; <var>string</var> (9) \"Sec-Ch-Ua\"</dt></dl><dl><dt><dfn>sec-ch-ua-mobile</dfn> =&gt; <var>string</var> (16) \"Sec-Ch-Ua-Mobile\"</dt></dl><dl><dt><dfn>sec-ch-ua-platform</dfn> =&gt; <var>string</var> (18) \"Sec-Ch-Ua-Platform\"</dt></dl><dl><dt><dfn>upgrade-insecure-requests</dfn> =&gt; <var>string</var> (25) \"Upgrade-Insecure-Requests\"</dt></dl><dl><dt><dfn>user-agent</dfn> =&gt; <var>string</var> (10) \"User-Agent\"</dt></dl><dl><dt><dfn>accept</dfn> =&gt; <var>string</var> (6) \"Accept\"</dt></dl><dl><dt><dfn>sec-fetch-site</dfn> =&gt; <var>string</var> (14) \"Sec-Fetch-Site\"</dt></dl><dl><dt><dfn>sec-fetch-mode</dfn> =&gt; <var>string</var> (14) \"Sec-Fetch-Mode\"</dt></dl><dl><dt><dfn>sec-fetch-user</dfn> =&gt; <var>string</var> (14) \"Sec-Fetch-User\"</dt></dl><dl><dt><dfn>sec-fetch-dest</dfn> =&gt; <var>string</var> (14) \"Sec-Fetch-Dest\"</dt></dl><dl><dt><dfn>referer</dfn> =&gt; <var>string</var> (7) \"Referer\"</dt></dl><dl><dt><dfn>accept-encoding</dfn> =&gt; <var>string</var> (15) \"Accept-Encoding\"</dt></dl><dl><dt><dfn>accept-language</dfn> =&gt; <var>string</var> (15) \"Accept-Language\"</dt></dl><dl><dt><dfn>cookie</dfn> =&gt; <var>string</var> (6) \"Cookie\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>method</dfn> -&gt; <var>string</var> (3) \"GET\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>uri</dfn> -&gt; <var>CodeIgniter\\HTTP\\SiteURI</var>#23 (20)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (20)</li><li>Methods (51)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (12) \"email-sender\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (3) \"new\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (25) \"/codei46/email-sender/new\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>baseURL</dfn> -&gt; <var>CodeIgniter\\HTTP\\URI</var>#24 (15)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (15)</li><li>Methods (40)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (1) <var>Depth Limit</var></dt></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (9) \"/codei46/\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4) <var>Depth Limit</var></dt></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?string $uri = null)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param string|null $uri The URI to parse.\n *\n * @throws HTTPException\n *\n * @TODO null for param $uri should be removed.\n *      See https://www.php-fig.org/psr/psr-17/#26-urifactoryinterface\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:256</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn>: <var>URI</var> Sets and overwrites any current URI information.</dt><dd><pre>/**\n * Sets and overwrites any current URI information.\n *\n * @return URI\n *\n * @throws HTTPException\n *\n * @deprecated 4.4.0 This method will be private.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:302</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n *\n * Warning: For backwards-compatability this method\n * assumes URIs with the same host as baseURL should\n * be relative to the project's configuration.\n * This aspect of __toString() is deprecated and should be avoided.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:628</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:651</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the path portion of the URI.</dt><dd><pre>/**\n * Sets the path portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPath($port)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:816</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var> Sets the current baseURL.</dt><dd><pre>/**\n * Sets the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:834</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the current baseURL.</dt><dd><pre>/**\n * Returns the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:846</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:862</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url call.</dt><dd><pre>/**\n * Saves our parts from a parse_url call.\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1036</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1165</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>baseURL</dfn> <var>string</var> (25) \"http://localhost/codei46/\"</dt></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>basePathWithoutIndexPage</dfn> -&gt; <var>string</var> (9) \"/codei46/\"</dt></dl><dl><dt><var>private readonly</var> <dfn>indexPage</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>baseSegments</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (7) \"codei46\"</dt></dl></dd></dl><dl><dt><var>private</var> <dfn>routePath</dfn> -&gt; <var>string</var> (16) \"email-sender/new\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\App $configApp, string $relativePath = '', ?string $host = null, ?string $scheme = null)</dfn></dt><dd><pre>/**\n * @param         string              $relativePath URI path relative to baseURL. May include\n *                                                  queries or fragments.\n * @param         string|null         $host         Optional current hostname.\n * @param         string|null         $scheme       Optional scheme. 'http' or 'https'.\n * @phpstan-param 'http'|'https'|null $scheme\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:94</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>parseRelativePath(string $relativePath)</dfn>: <var>array</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>determineBaseURL(Config\\App $configApp, ?string $host, ?string $scheme)</dfn>: <var>CodeIgniter\\HTTP\\URI</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:142</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>getIndexPageRoutePath(string $routePath)</dfn>: <var>string</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>normalizeBaseURL(Config\\App $configApp)</dfn>: <var>string</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:193</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setBasePath()</dfn>: <var>void</var> Sets basePathWithoutIndexPage and baseSegments.</dt><dd><pre>/**\n * Sets basePathWithoutIndexPage and baseSegments.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:212</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:234</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the baseURL.</dt><dd><pre>/**\n * Returns the baseURL.\n *\n * @interal\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:244</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRoutePath()</dfn>: <var>string</var> Returns the URI path relative to baseURL.</dt><dd><pre>/**\n * Returns the URI path relative to baseURL.\n *\n * @return string The Route path.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:262</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:278</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setRoutePath(string $routePath)</dfn>: <var>void</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:288</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>convertToSegments(string $path)</dfn>: <var>array</var> Converts path to segments</dt><dd><pre>/**\n * Converts path to segments\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:304</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url() call.</dt><dd><pre>/**\n * Saves our parts from a parse_url() call.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:335</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>baseUrl($relativePath = '', ?string $scheme = null)</dfn>: <var>string</var> For base_url() helper.</dt><dd><pre>/**\n * For base_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:379</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>stringifyRelativePath($relativePath)</dfn>: <var>string</var></dt><dd><pre>/**\n * @param array|string $relativePath URI string or array of URI segments\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:401</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>siteUrl($relativePath = '', ?string $scheme = null, ?Config\\App $config = null)</dfn>: <var>string</var> For site_url() helper.</dt><dd><pre>/**\n * For site_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n * @param App|null     $config       Alternate configuration to use.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/SiteURI.php:419</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:1165</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:651</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>uri</dfn> <var>string</var> (41) \"http://localhost/codei46/email-sender/new\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>config</dfn> -&gt; <var>Config\\App</var>#15 (13)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (13)</li><li>Methods (4)</li><li>Static methods (3)</li><li>Static properties (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>baseURL</dfn> -&gt; <var>string</var> (25) \"http://localhost/codei46/\"</dt></dl><dl><dt><var>public</var> <dfn>allowedHostnames</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>public</var> <dfn>indexPage</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>public</var> <dfn>uriProtocol</dfn> -&gt; <var>string</var> (11) \"REQUEST_URI\"</dt></dl><dl><dt><var>public</var> <dfn>permittedURIChars</dfn> -&gt; <var>string</var> (14) \"a-z 0-9~%.:_\\-\"</dt></dl><dl><dt><var>public</var> <dfn>defaultLocale</dfn> -&gt; <var>string</var> (2) \"en\"</dt></dl><dl><dt><var>public</var> <dfn>negotiateLocale</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>supportedLocales</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (2) \"en\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>appTimezone</dfn> -&gt; <var>string</var> (3) \"UTC\"</dt></dl><dl><dt><var>public</var> <dfn>charset</dfn> -&gt; <var>string</var> (5) \"UTF-8\"</dt></dl><dl><dt><var>public</var> <dfn>forceGlobalSecureRequests</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>public</var> <dfn>proxyIPs</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>public</var> <dfn>CSPEnabled</dfn> -&gt; <var>boolean</var> false</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn> Will attempt to get environment variables with names that match the propertie...</dt><dd><pre>/**\n * Will attempt to get environment variables with names\n * that match the properties of the child class.\n *\n * The \"shortPrefix\" is the lowercase-only config class name.\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:114</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>initEnvValue(&amp;$property, string $name, string $prefix, string $shortPrefix)</dfn>: <var>void</var> Initialization an environment-specific configuration setting</dt><dd><pre>/**\n * Initialization an environment-specific configuration setting\n *\n * @param array|bool|float|int|string|null $property\n *\n * @return void\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:151</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getEnvValue(string $property, string $prefix, string $shortPrefix)</dfn>: <var>string|null</var> Retrieve an environment-specific configuration setting</dt><dd><pre>/**\n * Retrieve an environment-specific configuration setting\n *\n * @return string|null\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>registerProperties()</dfn>: <var>void</var> Provides external libraries a simple way to register one or more options into...</dt><dd><pre>/**\n * Provides external libraries a simple way to register one or more\n * options into a config file.\n *\n * @return void\n *\n * @throws ReflectionException\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:237</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:72</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::setModules(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::reset()</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:100</small></pre></dd></dl></li><li><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrars</dfn> :: <var>array</var> (0)</dt></dl><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$override</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$didDiscovery</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$discovering</dfn> :: <var>boolean</var> false</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrarFile</dfn> :: <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$moduleConfig</dfn> :: <var>Config\\Modules</var>#7 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (2)</li><li>Static methods (1)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>enabled</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>public</var> <dfn>discoverInComposer</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>public</var> <dfn>aliases</dfn> -&gt; <var>array</var> (5) <var>Depth Limit</var></dt></dl><dl><dt><var>public</var> <dfn>composerPackages</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:46</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>shouldDiscover(string $alias)</dfn>: <var>bool</var> Should the application auto-discover the requested resource.</dt><dd><pre>/**\n * Should the application auto-discover the requested resource.\n */\n\n<small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:54</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Modules\\Modules::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:63</small></pre></dd></dl></li></ul></dd></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>ipAddress</dfn> -&gt; <var>string</var> (3) \"::1\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>globals</dfn> -&gt; <var>array</var> (3)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>server</dfn> =&gt; <var>array</var> (68)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_REDIRECT_MIBDIRS</dfn> =&gt; <var>string</var> (24) \"C:/xampp/php/extras/mibs\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jan 29 2024 C:/xampp/php/extras/mibs\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_REDIRECT_MYSQL_HOME</dfn> =&gt; <var>string</var> (16) \"\\xampp\\mysql\\bin\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jan 29 2024 \\xampp\\mysql\\bin\n</pre></li></ul></dd></dl><dl><dt><dfn>REDIRECT_REDIRECT_OPENSSL_CONF</dfn> =&gt; <var>string</var> (31) \"C:/xampp/apache/bin/openssl.cnf\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_REDIRECT_PHP_PEAR_SYSCONF_DIR</dfn> =&gt; <var>string</var> (10) \"\\xampp\\php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Apr 07 09:53 \\xampp\\php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_REDIRECT_PHPRC</dfn> =&gt; <var>string</var> (10) \"\\xampp\\php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Apr 07 09:53 \\xampp\\php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_REDIRECT_TMP</dfn> =&gt; <var>string</var> (10) \"\\xampp\\tmp\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 15 03:49 \\xampp\\tmp\n</pre></li></ul></dd></dl><dl><dt><dfn>REDIRECT_REDIRECT_STATUS</dfn> =&gt; <var>string</var> (3) \"200\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_MIBDIRS</dfn> =&gt; <var>string</var> (24) \"C:/xampp/php/extras/mibs\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jan 29 2024 C:/xampp/php/extras/mibs\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_MYSQL_HOME</dfn> =&gt; <var>string</var> (16) \"\\xampp\\mysql\\bin\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jan 29 2024 \\xampp\\mysql\\bin\n</pre></li></ul></dd></dl><dl><dt><dfn>REDIRECT_OPENSSL_CONF</dfn> =&gt; <var>string</var> (31) \"C:/xampp/apache/bin/openssl.cnf\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_PHP_PEAR_SYSCONF_DIR</dfn> =&gt; <var>string</var> (10) \"\\xampp\\php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Apr 07 09:53 \\xampp\\php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_PHPRC</dfn> =&gt; <var>string</var> (10) \"\\xampp\\php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Apr 07 09:53 \\xampp\\php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REDIRECT_TMP</dfn> =&gt; <var>string</var> (10) \"\\xampp\\tmp\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 15 03:49 \\xampp\\tmp\n</pre></li></ul></dd></dl><dl><dt><dfn>REDIRECT_STATUS</dfn> =&gt; <var>string</var> (3) \"200\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>MIBDIRS</dfn> =&gt; <var>string</var> (24) \"C:/xampp/php/extras/mibs\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jan 29 2024 C:/xampp/php/extras/mibs\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>MYSQL_HOME</dfn> =&gt; <var>string</var> (16) \"\\xampp\\mysql\\bin\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jan 29 2024 \\xampp\\mysql\\bin\n</pre></li></ul></dd></dl><dl><dt><dfn>OPENSSL_CONF</dfn> =&gt; <var>string</var> (31) \"C:/xampp/apache/bin/openssl.cnf\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>PHP_PEAR_SYSCONF_DIR</dfn> =&gt; <var>string</var> (10) \"\\xampp\\php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Apr 07 09:53 \\xampp\\php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>PHPRC</dfn> =&gt; <var>string</var> (10) \"\\xampp\\php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Apr 07 09:53 \\xampp\\php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>TMP</dfn> =&gt; <var>string</var> (10) \"\\xampp\\tmp\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 15 03:49 \\xampp\\tmp\n</pre></li></ul></dd></dl><dl><dt><dfn>HTTP_HOST</dfn> =&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><dfn>HTTP_CONNECTION</dfn> =&gt; <var>string</var> (10) \"keep-alive\"</dt></dl><dl><dt><dfn>HTTP_CACHE_CONTROL</dfn> =&gt; <var>string</var> (9) \"max-age=0\"</dt></dl><dl><dt><dfn>HTTP_SEC_CH_UA</dfn> =&gt; <var>string</var> (65) \"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"\"</dt></dl><dl><dt><dfn>HTTP_SEC_CH_UA_MOBILE</dfn> =&gt; <var>string</var> (2) \"?0\"</dt></dl><dl><dt><dfn>HTTP_SEC_CH_UA_PLATFORM</dfn> =&gt; <var>string</var> (9) \"\"Windows\"\"</dt></dl><dl><dt><dfn>HTTP_UPGRADE_INSECURE_REQUESTS</dfn> =&gt; <var>string</var> (1) \"1\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>HTTP_USER_AGENT</dfn> =&gt; <var>string</var> (111) \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Ge...</dt><dd><pre>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n</pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>HTTP_ACCEPT</dfn> =&gt; <var>string</var> (135) \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,...</dt><dd><pre>text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n</pre></dd></dl><dl><dt><dfn>HTTP_SEC_FETCH_SITE</dfn> =&gt; <var>string</var> (11) \"same-origin\"</dt></dl><dl><dt><dfn>HTTP_SEC_FETCH_MODE</dfn> =&gt; <var>string</var> (8) \"navigate\"</dt></dl><dl><dt><dfn>HTTP_SEC_FETCH_USER</dfn> =&gt; <var>string</var> (2) \"?1\"</dt></dl><dl><dt><dfn>HTTP_SEC_FETCH_DEST</dfn> =&gt; <var>string</var> (8) \"document\"</dt></dl><dl><dt><dfn>HTTP_REFERER</dfn> =&gt; <var>string</var> (37) \"http://localhost/codei46/email-sender\"</dt></dl><dl><dt><dfn>HTTP_ACCEPT_ENCODING</dfn> =&gt; <var>string</var> (23) \"gzip, deflate, br, zstd\"</dt></dl><dl><dt><dfn>HTTP_ACCEPT_LANGUAGE</dfn> =&gt; <var>string</var> (14) \"en-US,en;q=0.9\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>HTTP_COOKIE</dfn> =&gt; <var>string</var> (94) \"csrf_cookie_name=2dad20e4de25624936e6bca004bb1ba1; ci_session=e0977661fad7ae...</dt><dd><pre>csrf_cookie_name=2dad20e4de25624936e6bca004bb1ba1; ci_session=e0977661fad7aee083577aa7804c3fbf\n</pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>PATH</dfn> =&gt; <var>string</var> (1485) \"C:\\Program Files\\ImageMagick-7.1.1-Q16-HDRI;C:\\Program Files\\Tesseract-OCR;C...</dt><dd><pre>C:\\Program Files\\ImageMagick-7.1.1-Q16-HDRI;C:\\Program Files\\Tesseract-OCR;C:\\Program Files\\Python311\\;C:\\Program Files\\Python311\\Scripts\\;C:\\Python27\\;C:\\Python27\\Scripts;C:\\Program Files\\CMake\\bin;C:\\Program Files\\Java\\jdk-17;C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;\"C:\\Program Files\\gs\\gs10.00.0\\bin;C:\\Program Files\\gs\\gs10.00.0\\lib;\";C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Program Files\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\Scripts;C:\\src\\poppler-23.09.0\\bin;C:\\Android\\android-sdk;C:\\src\\flutter\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\nvm;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\PuTTY\\;C:\\Program Files\\Void\\bin;C:\\Program Files\\VSCodium\\bin;C:\\Program Files (x86)\\HP\\Common\\HPDestPlgIn\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin\n</pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>SystemRoot</dfn> =&gt; <var>string</var> (10) \"C:\\WINDOWS\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 02 16:33 C:\\WINDOWS\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>COMSPEC</dfn> =&gt; <var>string</var> (27) \"C:\\WINDOWS\\system32\\cmd.exe\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (368KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rwxrwxrwx 0 <USER> <GROUP> Jun 02 15:58 C:\\WINDOWS\\system32\\cmd.exe\n</pre></li></ul></dd></dl><dl><dt><dfn>PATHEXT</dfn> =&gt; <var>string</var> (62) \".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>WINDIR</dfn> =&gt; <var>string</var> (10) \"C:\\WINDOWS\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 02 16:33 C:\\WINDOWS\n</pre></li></ul></dd></dl><dl><dt><dfn>SERVER_SIGNATURE</dfn> =&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><dfn>SERVER_SOFTWARE</dfn> =&gt; <var>string</var> (46) \"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12\"</dt></dl><dl><dt><dfn>SERVER_NAME</dfn> =&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><dfn>SERVER_ADDR</dfn> =&gt; <var>string</var> (3) \"::1\"</dt></dl><dl><dt><dfn>SERVER_PORT</dfn> =&gt; <var>string</var> (2) \"80\"</dt></dl><dl><dt><dfn>REMOTE_ADDR</dfn> =&gt; <var>string</var> (3) \"::1\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>DOCUMENT_ROOT</dfn> =&gt; <var>string</var> (15) \"C:/xampp/htdocs\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 12 03:24 C:/xampp/htdocs\n</pre></li></ul></dd></dl><dl><dt><dfn>REQUEST_SCHEME</dfn> =&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><dfn>CONTEXT_PREFIX</dfn> =&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>CONTEXT_DOCUMENT_ROOT</dfn> =&gt; <var>string</var> (15) \"C:/xampp/htdocs\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 12 03:24 C:/xampp/htdocs\n</pre></li></ul></dd></dl><dl><dt><dfn>SERVER_ADMIN</dfn> =&gt; <var>string</var> (20) \"postmaster@localhost\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>SCRIPT_FILENAME</dfn> =&gt; <var>string</var> (40) \"C:/xampp/htdocs/codei46/public/index.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (1.7KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:/xampp/htdocs/codei46/public/index.php\n</pre></li></ul></dd></dl><dl><dt><dfn>REMOTE_PORT</dfn> =&gt; <var>string</var> (5) \"54008\"</dt></dl><dl><dt><dfn>REDIRECT_URL</dfn> =&gt; <var>string</var> (32) \"/codei46/public/email-sender/new\"</dt></dl><dl><dt><dfn>GATEWAY_INTERFACE</dfn> =&gt; <var>string</var> (7) \"CGI/1.1\"</dt></dl><dl><dt><dfn>SERVER_PROTOCOL</dfn> =&gt; <var>string</var> (8) \"HTTP/1.1\"</dt></dl><dl><dt><dfn>REQUEST_METHOD</dfn> =&gt; <var>string</var> (3) \"GET\"</dt></dl><dl><dt><dfn>QUERY_STRING</dfn> =&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><dfn>REQUEST_URI</dfn> =&gt; <var>string</var> (25) \"/codei46/email-sender/new\"</dt></dl><dl><dt><dfn>SCRIPT_NAME</dfn> =&gt; <var>string</var> (25) \"/codei46/public/index.php\"</dt></dl><dl><dt><dfn>PATH_INFO</dfn> =&gt; <var>string</var> (17) \"/email-sender/new\"</dt></dl><dl><dt><dfn>PATH_TRANSLATED</dfn> =&gt; <var>string</var> (32) \"C:\\xampp\\htdocs\\email-sender\\new\"</dt></dl><dl><dt><dfn>PHP_SELF</dfn> =&gt; <var>string</var> (42) \"/codei46/public/index.php/email-sender/new\"</dt></dl><dl><dt><dfn>REQUEST_TIME_FLOAT</dfn> =&gt; <var>double</var> **********.4493</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>REQUEST_TIME</dfn> =&gt; <var>integer</var> **********</dt><dd><pre>2025-06-15T05:40:01+00:00\n</pre></dd></dl><dl><dt><dfn>CI_ENVIRONMENT</dfn> =&gt; <var>string</var> (11) \"development\"</dt></dl></dd></dl><dl><dt><dfn>get</dfn> =&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>cookie</dfn> =&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>csrf_cookie_name</dfn> =&gt; <var>string</var> (32) \"2dad20e4de25624936e6bca004bb1ba1\"</dt></dl><dl><dt><dfn>ci_session</dfn> =&gt; <var>string</var> (32) \"e0977661fad7aee083577aa7804c3fbf\"</dt></dl></dd></dl></dd></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (16) \"email-sender/new\"</dt></dl><dl><dt><var>protected</var> <dfn>files</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>negotiator</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>defaultLocale</dfn> -&gt; <var>string</var> (2) \"en\"</dt></dl><dl><dt><var>protected</var> <dfn>locale</dfn> -&gt; <var>string</var> (2) \"en\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>validLocales</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (2) \"en\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>oldInput</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>userAgent</dfn> -&gt; <var>CodeIgniter\\HTTP\\UserAgent</var>#25 (11)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (11)</li><li>Methods (19)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>agent</dfn> -&gt; <var>string</var> (111) \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Ge...</dt><dd><pre>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n</pre></dd></dl><dl><dt><var>protected</var> <dfn>isBrowser</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>protected</var> <dfn>isRobot</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>isMobile</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>config</dfn> -&gt; <var>Config\\UserAgents</var>#26 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (4)</li><li>Static methods (3)</li><li>Static properties (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>public</var> <dfn>platforms</dfn> -&gt; <var>array</var> (42) <var>Depth Limit</var></dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>public</var> <dfn>browsers</dfn> -&gt; <var>array</var> (30) <var>Depth Limit</var></dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>public</var> <dfn>mobiles</dfn> -&gt; <var>array</var> (75) <var>Depth Limit</var></dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>public</var> <dfn>robots</dfn> -&gt; <var>array</var> (19) <var>Depth Limit</var></dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn> Will attempt to get environment variables with names that match the propertie...</dt><dd><pre>/**\n * Will attempt to get environment variables with names\n * that match the properties of the child class.\n *\n * The \"shortPrefix\" is the lowercase-only config class name.\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:114</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>initEnvValue(&amp;$property, string $name, string $prefix, string $shortPrefix)</dfn>: <var>void</var> Initialization an environment-specific configuration setting</dt><dd><pre>/**\n * Initialization an environment-specific configuration setting\n *\n * @param array|bool|float|int|string|null $property\n *\n * @return void\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:151</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getEnvValue(string $property, string $prefix, string $shortPrefix)</dfn>: <var>string|null</var> Retrieve an environment-specific configuration setting</dt><dd><pre>/**\n * Retrieve an environment-specific configuration setting\n *\n * @return string|null\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>registerProperties()</dfn>: <var>void</var> Provides external libraries a simple way to register one or more options into...</dt><dd><pre>/**\n * Provides external libraries a simple way to register one or more\n * options into a config file.\n *\n * @return void\n *\n * @throws ReflectionException\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:237</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:72</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::setModules(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::reset()</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:100</small></pre></dd></dl></li><li><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrars</dfn> :: <var>array</var> (0)</dt></dl><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$override</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$didDiscovery</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$discovering</dfn> :: <var>boolean</var> false</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrarFile</dfn> :: <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$moduleConfig</dfn> :: <var>Config\\Modules</var>#7 <var>Depth Limit</var></dt></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>platform</dfn> -&gt; <var>string</var> (10) \"Windows 10\"</dt></dl><dl><dt><var>protected</var> <dfn>browser</dfn> -&gt; <var>string</var> (6) \"Chrome\"</dt></dl><dl><dt><var>protected</var> <dfn>version</dfn> -&gt; <var>string</var> (9) \"*********\"</dt></dl><dl><dt><var>protected</var> <dfn>mobile</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>robot</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>referrer</dfn> -&gt; <var>null</var></dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?Config\\UserAgents $config = null)</dfn> Constructor</dt><dd><pre>/**\n * Constructor\n *\n * Sets the User Agent and runs the compilation routine\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:108</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>isBrowser(?string $key = null)</dfn>: <var>bool</var> Is Browser</dt><dd><pre>/**\n * Is Browser\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:121</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>isRobot(?string $key = null)</dfn>: <var>bool</var> Is Robot</dt><dd><pre>/**\n * Is Robot\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:139</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>isMobile(?string $key = null)</dfn>: <var>bool</var> Is Mobile</dt><dd><pre>/**\n * Is Mobile\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:157</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>isReferral()</dfn>: <var>bool</var> Is this a referral from another site?</dt><dd><pre>/**\n * Is this a referral from another site?\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:175</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAgentString()</dfn>: <var>string</var> Agent String</dt><dd><pre>/**\n * Agent String\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:194</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPlatform()</dfn>: <var>string</var> Get Platform</dt><dd><pre>/**\n * Get Platform\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:202</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBrowser()</dfn>: <var>string</var> Get Browser Name</dt><dd><pre>/**\n * Get Browser Name\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:210</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getVersion()</dfn>: <var>string</var> Get the Browser Version</dt><dd><pre>/**\n * Get the Browser Version\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:218</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRobot()</dfn>: <var>string</var> Get The Robot Name</dt><dd><pre>/**\n * Get The Robot Name\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getMobile()</dfn>: <var>string</var> Get the Mobile Device</dt><dd><pre>/**\n * Get the Mobile Device\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:234</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getReferrer()</dfn>: <var>string</var> Get the referrer</dt><dd><pre>/**\n * Get the referrer\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:242</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>parse(string $string)</dfn>: <var>void</var> Parse a custom user-agent string</dt><dd><pre>/**\n * Parse a custom user-agent string\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:252</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>compileData()</dfn>: <var>void</var> Compile the User Agent Data</dt><dd><pre>/**\n * Compile the User Agent Data\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:276</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>setPlatform()</dfn>: <var>bool</var> Set the Platform</dt><dd><pre>/**\n * Set the Platform\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:290</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>setBrowser()</dfn>: <var>bool</var> Set the Browser</dt><dd><pre>/**\n * Set the Browser\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:310</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>setRobot()</dfn>: <var>bool</var> Set the Robot</dt><dd><pre>/**\n * Set the Robot\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:331</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>setMobile()</dfn>: <var>bool</var> Set the Mobile Device</dt><dd><pre>/**\n * Set the Mobile Device\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:351</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Outputs the original Agent String when cast as a string.</dt><dd><pre>/**\n * Outputs the original Agent String when cast as a string.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/UserAgent.php:370</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><dfn>userAgent</dfn> <var>string</var> (111) \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Ge...</dt><dd><pre>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n</pre></dd></dl></li></ul></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct($config, ?CodeIgniter\\HTTP\\URI $uri = null, $body = 'php://input', ?CodeIgniter\\HTTP\\UserAgent $userAgent = null)</dfn> Constructor</dt><dd><pre>/**\n * Constructor\n *\n * @param App         $config\n * @param string|null $body\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:132</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>getPostMaxSize()</dfn>: <var>int</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:172</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>detectLocale($config)</dfn>: <var>void</var> Handles setting up the locale, perhaps auto-detecting through content negotia...</dt><dd><pre>/**\n * Handles setting up the locale, perhaps auto-detecting through\n * content negotiation.\n *\n * @param App $config\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:192</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>detectURI(string $protocol, string $baseURL)</dfn>: <var>void</var> Sets up our URI object based on the information we have. This is either provi...</dt><dd><pre>/**\n * Sets up our URI object based on the information we have. This is\n * either provided by the user in the baseURL Config setting, or\n * determined from the environment as needed.\n *\n * @return void\n *\n * @deprecated 4.4.0 No longer used.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:212</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>detectPath(string $protocol = '')</dfn>: <var>string</var> Detects the relative path based on the URIProtocol Config setting.</dt><dd><pre>/**\n * Detects the relative path based on\n * the URIProtocol Config setting.\n *\n * @deprecated 4.4.0 Moved to SiteURIFactory.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:223</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseRequestURI()</dfn>: <var>string</var> Will parse the REQUEST_URI and automatically detect the URI from it, fixing t...</dt><dd><pre>/**\n * Will parse the REQUEST_URI and automatically detect the URI from it,\n * fixing the query string if necessary.\n *\n * @return string The URI it found.\n *\n * @deprecated 4.4.0 Moved to SiteURIFactory.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:246</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseQueryString()</dfn>: <var>string</var> Parse QUERY_STRING</dt><dd><pre>/**\n * Parse QUERY_STRING\n *\n * Will parse QUERY_STRING and automatically detect the URI from it.\n *\n * @deprecated 4.4.0 Moved to SiteURIFactory.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:306</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>negotiate(string $type, array $supported, bool $strictMatch = false)</dfn>: <var>string</var> Provides a convenient way to work with the Negotiate class for content negoti...</dt><dd><pre>/**\n * Provides a convenient way to work with the Negotiate class\n * for content negotiation.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:334</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is(string $type)</dfn>: <var>bool</var> Checks this request type.</dt><dd><pre>/**\n * Checks this request type.\n *\n * @param         string                                                                    $type HTTP verb or 'json' or 'ajax'\n * @phpstan-param string|'get'|'post'|'put'|'delete'|'head'|'patch'|'options'|'json'|'ajax' $type\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:355</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>isCLI()</dfn>: <var>bool</var> Determines if this request was made from the command line (CLI).</dt><dd><pre>/**\n * Determines if this request was made from the command line (CLI).\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:379</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>isAJAX()</dfn>: <var>bool</var> Test to see if a request contains the HTTP_X_REQUESTED_WITH header.</dt><dd><pre>/**\n * Test to see if a request contains the HTTP_X_REQUESTED_WITH header.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:387</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>isSecure()</dfn>: <var>bool</var> Attempts to detect if the current connection is secure through a few differen...</dt><dd><pre>/**\n * Attempts to detect if the current connection is secure through\n * a few different methods.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:397</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path, ?Config\\App $config = null)</dfn>: <var>$this</var> Sets the URI path relative to baseURL.</dt><dd><pre>/**\n * Sets the URI path relative to baseURL.\n *\n * Note: Since current_url() accesses the shared request\n * instance, this can be used to change the \"current URL\"\n * for testing.\n *\n * @param string   $path   URI path relative to baseURL\n * @param App|null $config Optional alternate config to use\n *\n * @return $this\n *\n * @deprecated 4.4.0 This method will be private. The parameter $config is deprecated. No longer used.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:424</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Returns the URI path relative to baseURL, running detection as necessary.</dt><dd><pre>/**\n * Returns the URI path relative to baseURL,\n * running detection as necessary.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:435</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setLocale(string $locale)</dfn>: <var>IncomingRequest</var> Sets the locale string for this request.</dt><dd><pre>/**\n * Sets the locale string for this request.\n *\n * @return IncomingRequest\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:445</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setValidLocales(array $locales)</dfn>: <var>$this</var> Set the valid locales.</dt><dd><pre>/**\n * Set the valid locales.\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:464</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getLocale()</dfn>: <var>string</var> Gets the current locale, with a fallback to the default locale if none is set.</dt><dd><pre>/**\n * Gets the current locale, with a fallback to the default\n * locale if none is set.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:475</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getDefaultLocale()</dfn>: <var>string</var> Returns the default locale as set in app/Config/App.php</dt><dd><pre>/**\n * Returns the default locale as set in app/Config/App.php\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:483</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getVar($index = null, $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|stdClass|string|null</var> Fetch an item from JSON input stream with fallback to $_REQUEST object. This ...</dt><dd><pre>/**\n * Fetch an item from JSON input stream with fallback to $_REQUEST object. This is the simplest way\n * to grab data from the request object and can be used in lieu of the\n * other get* methods in most cases.\n *\n * @param array|string|null $index\n * @param int|null          $filter Filter constant\n * @param array|int|null    $flags\n *\n * @return array|bool|float|int|stdClass|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:499</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getJSON(bool $assoc = false, int $depth = 512, int $options = 0)</dfn>: <var>array|bool|float|int|stdClass|null</var> A convenience method that grabs the raw input stream and decodes the JSON int...</dt><dd><pre>/**\n * A convenience method that grabs the raw input stream and decodes\n * the JSON into an array.\n *\n * If $assoc == true, then all objects in the response will be converted\n * to associative arrays.\n *\n * @param bool $assoc   Whether to return objects as associative arrays\n * @param int  $depth   How many levels deep to decode\n * @param int  $options Bitmask of options\n *\n * @see http://php.net/manual/en/function.json-decode.php\n *\n * @return array|bool|float|int|stdClass|null\n *\n * @throws HTTPException When the body is invalid as JSON.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:528</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getJsonVar($index = null, bool $assoc = false, ?int $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|stdClass|string|null</var> Get a specific variable from a JSON input stream</dt><dd><pre>/**\n * Get a specific variable from a JSON input stream\n *\n * @param array|string|null $index  The variable that you want which can use dot syntax for getting specific values.\n * @param bool              $assoc  If true, return the result as an associative array.\n * @param int|null          $filter Filter Constant\n * @param array|int|null    $flags  Option\n *\n * @return array|bool|float|int|stdClass|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:553</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRawInput()</dfn>: <var>array</var> A convenience method that grabs the raw input stream(send method in PUT, PATC...</dt><dd><pre>/**\n * A convenience method that grabs the raw input stream(send method in PUT, PATCH, DELETE) and decodes\n * the String into an array.\n *\n * @return array\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:628</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRawInputVar($index = null, ?int $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|object|string|null</var> Gets a specific variable from raw input stream (send method in PUT, PATCH, DE...</dt><dd><pre>/**\n * Gets a specific variable from raw input stream (send method in PUT, PATCH, DELETE).\n *\n * @param array|string|null $index  The variable that you want which can use dot syntax for getting specific values.\n * @param int|null          $filter Filter Constant\n * @param array|int|null    $flags  Option\n *\n * @return array|bool|float|int|object|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:644</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getGet($index = null, $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|object|string|null</var> Fetch an item from GET data.</dt><dd><pre>/**\n * Fetch an item from GET data.\n *\n * @param array|string|null $index  Index for item to fetch from $_GET.\n * @param int|null          $filter A filter name to apply.\n * @param array|int|null    $flags\n *\n * @return array|bool|float|int|object|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:698</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPost($index = null, $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|object|string|null</var> Fetch an item from POST.</dt><dd><pre>/**\n * Fetch an item from POST.\n *\n * @param array|string|null $index  Index for item to fetch from $_POST.\n * @param int|null          $filter A filter name to apply\n * @param array|int|null    $flags\n *\n * @return array|bool|float|int|object|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:712</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPostGet($index = null, $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|object|string|null</var> Fetch an item from POST data with fallback to GET.</dt><dd><pre>/**\n * Fetch an item from POST data with fallback to GET.\n *\n * @param array|string|null $index  Index for item to fetch from $_POST or $_GET\n * @param int|null          $filter A filter name to apply\n * @param array|int|null    $flags\n *\n * @return array|bool|float|int|object|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:726</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getGetPost($index = null, $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|object|string|null</var> Fetch an item from GET data with fallback to POST.</dt><dd><pre>/**\n * Fetch an item from GET data with fallback to POST.\n *\n * @param array|string|null $index  Index for item to be fetched from $_GET or $_POST\n * @param int|null          $filter A filter name to apply\n * @param array|int|null    $flags\n *\n * @return array|bool|float|int|object|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:749</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getCookie($index = null, $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|object|string|null</var> Fetch an item from the COOKIE array.</dt><dd><pre>/**\n * Fetch an item from the COOKIE array.\n *\n * @param array|string|null $index  Index for item to be fetched from $_COOKIE\n * @param int|null          $filter A filter name to be applied\n * @param array|int|null    $flags\n *\n * @return array|bool|float|int|object|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:772</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserAgent()</dfn>: <var>UserAgent</var> Fetch the user agent string</dt><dd><pre>/**\n * Fetch the user agent string\n *\n * @return UserAgent\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:782</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getOldInput(string $key)</dfn>: <var>array|string|null</var> Attempts to get old Input data that has been flashed to the session with redi...</dt><dd><pre>/**\n * Attempts to get old Input data that has been flashed to the session\n * with redirect_with_input(). It first checks for the data in the old\n * POST data, then the old GET data and finally check for dot arrays\n *\n * @return array|string|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:794</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFiles()</dfn>: <var>array</var> Returns an array of all files that have been uploaded with this request. Each...</dt><dd><pre>/**\n * Returns an array of all files that have been uploaded with this\n * request. Each file is represented by an UploadedFile instance.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:845</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFileMultiple(string $fileID)</dfn>: <var>array|null</var> Verify if a file exist, by the name of the input field used to upload it, in ...</dt><dd><pre>/**\n * Verify if a file exist, by the name of the input field used to upload it, in the collection\n * of uploaded files and if is have been uploaded with multiple option.\n *\n * @return array|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:860</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFile(string $fileID)</dfn>: <var>UploadedFile|null</var> Retrieves a single file by the name of the input field used to upload it.</dt><dd><pre>/**\n * Retrieves a single file by the name of the input field used\n * to upload it.\n *\n * @return UploadedFile|null\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/HTTP/IncomingRequest.php:875</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setMethod(string $method)</dfn>: <var>$this</var> Sets the request method. Used when spoofing the request.</dt><dd><pre>/**\n * Sets the request method. Used when spoofing the request.\n *\n * @return $this\n *\n * @deprecated 4.0.5 Use withMethod() instead for immutability\n *\n * @codeCoverageIgnore\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Request.php:54</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withMethod($method)</dfn>: <var>static</var> Returns an instance with the specified method.</dt><dd><pre>/**\n * Returns an instance with the specified method.\n *\n * @param string $method\n *\n * @return static\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Request.php:68</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUri()</dfn>: <var>URI</var> Retrieves the URI instance.</dt><dd><pre>/**\n * Retrieves the URI instance.\n *\n * @return URI\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Request.php:82</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getMethod()</dfn>: <var>string</var> Retrieves the HTTP method of the request.</dt><dd><pre>/**\n * Retrieves the HTTP method of the request.\n *\n * @return string Returns the request method (always uppercase)\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\OutgoingRequest\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/OutgoingRequest.php:75</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withUri(CodeIgniter\\HTTP\\URI $uri, $preserveHost = false)</dfn>: <var>static</var> Returns an instance with the provided URI.</dt><dd><pre>/**\n * Returns an instance with the provided URI.\n *\n * @param URI  $uri          New request URI to use.\n * @param bool $preserveHost Preserve the original state of the Host header.\n *\n * @return static\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\OutgoingRequest\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/OutgoingRequest.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBody()</dfn>: <var>string|null</var> Returns the Message's body.</dt><dd><pre>/**\n * Returns the Message's body.\n *\n * @return string|null\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Message.php:58</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHeaders()</dfn>: <var>array</var> Returns an array containing all headers.</dt><dd><pre>/**\n * Returns an array containing all headers.\n *\n * @return array&lt;string, Header&gt; An array of the request headers\n *\n * @deprecated Use Message::headers() to make room for PSR-7\n *\n * @TODO Incompatible return value with PSR-7\n *\n * @codeCoverageIgnore\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Message.php:74</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHeader(string $name)</dfn>: <var>array|Header|null</var> Returns a single header object. If multiple headers with the same name exist,...</dt><dd><pre>/**\n * Returns a single header object. If multiple headers with the same\n * name exist, then will return an array of header objects.\n *\n * @return array|Header|null\n *\n * @deprecated Use Message::header() to make room for PSR-7\n *\n * @TODO Incompatible return value with PSR-7\n *\n * @codeCoverageIgnore\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Message.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>hasHeader(string $name)</dfn>: <var>bool</var> Determines whether a header exists.</dt><dd><pre>/**\n * Determines whether a header exists.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Message.php:99</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHeaderLine(string $name)</dfn>: <var>string</var> Retrieves a comma-separated string of the values for a single header.</dt><dd><pre>/**\n * Retrieves a comma-separated string of the values for a single header.\n *\n * This method returns all of the header values of the given\n * case-insensitive header name as a string concatenated together using\n * a comma.\n *\n * NOTE: Not all header values may be appropriately represented using\n * comma concatenation. For such headers, use getHeader() instead\n * and supply your own delimiter when concatenating.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Message.php:117</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getProtocolVersion()</dfn>: <var>string</var> Returns the HTTP Protocol Version.</dt><dd><pre>/**\n * Returns the HTTP Protocol Version.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/Message.php:138</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBody($data)</dfn>: <var>self</var> Sets the body of the current message.</dt><dd><pre>/**\n * Sets the body of the current message.\n *\n * @param string $data\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:59</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendBody($data)</dfn>: <var>self</var> Appends data to the body of the current message.</dt><dd><pre>/**\n * Appends data to the body of the current message.\n *\n * @param string $data\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:73</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>populateHeaders()</dfn>: <var>void</var> Populates the $headers array with any headers the server knows about.</dt><dd><pre>/**\n * Populates the $headers array with any headers the server knows about.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:87</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>headers()</dfn>: <var>array</var> Returns an array containing all Headers.</dt><dd><pre>/**\n * Returns an array containing all Headers.\n *\n * @return array&lt;string, Header|list&lt;Header&gt;&gt; An array of the Header objects\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:114</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>header($name)</dfn>: <var>Header|list&lt;Header&gt;|null</var> Returns a single Header object. If multiple headers with the same name exist,...</dt><dd><pre>/**\n * Returns a single Header object. If multiple headers with the same\n * name exist, then will return an array of header objects.\n *\n * @param string $name\n *\n * @return Header|list&lt;Header&gt;|null\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:134</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHeader(string $name, $value)</dfn>: <var>self</var> Sets a header and it's value.</dt><dd><pre>/**\n * Sets a header and it's value.\n *\n * @param array|string|null $value\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:148</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>removeHeader(string $name)</dfn>: <var>self</var> Removes a header from the list of headers we track.</dt><dd><pre>/**\n * Removes a header from the list of headers we track.\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:195</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>appendHeader(string $name, ?string $value)</dfn>: <var>self</var> Adds an additional header value to any headers that accept multiple values (i...</dt><dd><pre>/**\n * Adds an additional header value to any headers that accept\n * multiple values (i.e. are an array or implement ArrayAccess)\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:209</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addHeader(string $name, string $value)</dfn>: <var>static</var> Adds a header (not a header value) with the same name. Use this only when you...</dt><dd><pre>/**\n * Adds a header (not a header value) with the same name.\n * Use this only when you set multiple headers with the same name,\n * typically, for `Set-Cookie`.\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:229</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>prependHeader(string $name, string $value)</dfn>: <var>self</var> Adds an additional header value to any headers that accept multiple values (i...</dt><dd><pre>/**\n * Adds an additional header value to any headers that accept\n * multiple values (i.e. are an array or implement ArrayAccess)\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:255</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getHeaderName(string $name)</dfn>: <var>string</var> Takes a header name in any case, and returns the normal-case version of the h...</dt><dd><pre>/**\n * Takes a header name in any case, and returns the\n * normal-case version of the header.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:270</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setProtocolVersion(string $version)</dfn>: <var>self</var> Sets the HTTP protocol version.</dt><dd><pre>/**\n * Sets the HTTP protocol version.\n *\n * @return $this\n *\n * @throws HTTPException For invalid protocols\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:282</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getIPAddress()</dfn>: <var>string</var> Gets the user's IP address.</dt><dd><pre>/**\n * Gets the user's IP address.\n *\n * @return string IP address if it can be detected.\n *                If the IP address is not a valid IP address,\n *                then will return '0.0.0.0'.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/RequestTrait.php:60</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getServer($index = null, $filter = null, $flags = null)</dfn>: <var>mixed</var> Fetch an item from the $_SERVER array.</dt><dd><pre>/**\n * Fetch an item from the $_SERVER array.\n *\n * @param array|string|null $index  Index for item to be fetched from $_SERVER\n * @param int|null          $filter A filter name to be applied\n * @param array|int|null    $flags\n *\n * @return mixed\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/RequestTrait.php:202</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getEnv($index = null, $filter = null, $flags = null)</dfn>: <var>mixed</var> Fetch an item from the $_ENV array.</dt><dd><pre>/**\n * Fetch an item from the $_ENV array.\n *\n * @param array|string|null $index  Index for item to be fetched from $_ENV\n * @param int|null          $filter A filter name to be applied\n * @param array|int|null    $flags\n *\n * @return mixed\n *\n * @deprecated 4.4.4 This method does not work from the beginning. Use `env()`.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/RequestTrait.php:218</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setGlobal(string $name, $value)</dfn>: <var>$this</var> Allows manually setting the value of PHP global, like $_GET, $_POST, etc.</dt><dd><pre>/**\n * Allows manually setting the value of PHP global, like $_GET, $_POST, etc.\n *\n * @param         string                                   $name  Supergrlobal name (lowercase)\n * @phpstan-param 'get'|'post'|'request'|'cookie'|'server' $name\n * @param         mixed                                    $value\n *\n * @return $this\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/RequestTrait.php:233</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>fetchGlobal(string $name, $index = null, ?int $filter = null, $flags = null)</dfn>: <var>array|bool|float|int|object|string|null</var> Fetches one or more items from a global, like cookies, get, post, etc. Can op...</dt><dd><pre>/**\n * Fetches one or more items from a global, like cookies, get, post, etc.\n * Can optionally filter the input when you retrieve it by passing in\n * a filter.\n *\n * If $type is an array, it must conform to the input allowed by the\n * filter_input_array method.\n *\n * http://php.net/manual/en/filter.filters.sanitize.php\n *\n * @param         string                                   $name   Supergrlobal name (lowercase)\n * @phpstan-param 'get'|'post'|'request'|'cookie'|'server' $name\n * @param         array|int|string|null                    $index\n * @param         int|null                                 $filter Filter constant\n * @param         array|int|null                           $flags  Options\n *\n * @return array|bool|float|int|object|string|null\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/RequestTrait.php:258</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>populateGlobals(string $name)</dfn>: <var>void</var> Saves a copy of the current state of one of several PHP globals, so we can re...</dt><dd><pre>/**\n * Saves a copy of the current state of one of several PHP globals,\n * so we can retrieve them later.\n *\n * @param         string                                   $name Superglobal name (lowercase)\n * @phpstan-param 'get'|'post'|'request'|'cookie'|'server' $name\n *\n * @return void\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/RequestTrait.php:349</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>CodeIgniter\\HTTP\\Request::getClientIP(string $header)</dfn>: <var>?string</var> Gets the client IP address from the HTTP header.</dt><dd><pre>/**\n * Gets the client IP address from the HTTP header.\n */\n\n<small>Inherited from CodeIgniter\\HTTP\\Request\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/RequestTrait.php:168</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>CodeIgniter\\HTTP\\OutgoingRequest::getHostFromUri(CodeIgniter\\HTTP\\URI $uri)</dfn>: <var>string</var></dt><dd><pre><small>Inherited from CodeIgniter\\HTTP\\OutgoingRequest\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/OutgoingRequest.php:63</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>CodeIgniter\\HTTP\\OutgoingRequest::isHostHeaderMissingOrEmpty()</dfn>: <var>bool</var></dt><dd><pre><small>Inherited from CodeIgniter\\HTTP\\OutgoingRequest\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/OutgoingRequest.php:155</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>CodeIgniter\\HTTP\\Message::hasMultipleHeaders(string $name)</dfn>: <var>bool</var></dt><dd><pre><small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:173</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>CodeIgniter\\HTTP\\Message::checkMultipleHeaders(string $name)</dfn>: <var>void</var></dt><dd><pre><small>Inherited from CodeIgniter\\HTTP\\Message\nDefined in &lt;ROOT&gt;/codei46/system/HTTP/MessageTrait.php:180</small></pre></dd></dl></li></ul></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?CodeIgniter\\HTTP\\RequestInterface $request = null)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:39</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>uploaded(?string $blank, string $name)</dfn>: <var>bool</var> Verifies that $name is the name of a valid uploaded file.</dt><dd><pre>/**\n * Verifies that $name is the name of a valid uploaded file.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:53</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>max_size(?string $blank, string $params)</dfn>: <var>bool</var> Verifies if the file's size in Kilobytes is no larger than the parameter.</dt><dd><pre>/**\n * Verifies if the file's size in Kilobytes is no larger than the parameter.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:85</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>is_image(?string $blank, string $params)</dfn>: <var>bool</var> Uses the mime config file to determine if a file is considered an \"image\", wh...</dt><dd><pre>/**\n * Uses the mime config file to determine if a file is considered an \"image\",\n * which for our purposes basically means that it's a raster image or svg.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:125</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>mime_in(?string $blank, string $params)</dfn>: <var>bool</var> Checks to see if an uploaded file's mime type matches one in the parameter.</dt><dd><pre>/**\n * Checks to see if an uploaded file's mime type matches one in the parameter.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>ext_in(?string $blank, string $params)</dfn>: <var>bool</var> Checks to see if an uploaded file's extension matches one in the parameter.</dt><dd><pre>/**\n * Checks to see if an uploaded file's extension matches one in the parameter.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:193</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>max_dims(?string $blank, string $params)</dfn>: <var>bool</var> Checks an uploaded file to verify that the dimensions are within a specified ...</dt><dd><pre>/**\n * Checks an uploaded file to verify that the dimensions are within\n * a specified allowable dimension.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>min_dims(?string $blank, string $params)</dfn>: <var>bool</var> Checks an uploaded file to verify that the dimensions are greater than a spec...</dt><dd><pre>/**\n * Checks an uploaded file to verify that the dimensions are greater than\n * a specified dimension.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/FileRules.php:274</small></pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>3</dfn> =&gt; <var>CodeIgniter\\Validation\\StrictRules\\CreditCardRules</var>#89 (1)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (1)</li><li>Methods (2)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>nonStrictCreditCardRules</dfn> -&gt; <var>CodeIgniter\\Validation\\CreditCardRules</var>#90 (1)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (1)</li><li>Methods (2)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>cards</dfn> -&gt; <var>array</var> (21)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (21)</li><li>Contents (21)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>name</th><th>length</th><th>prefixes</th><th>checkdigit</th></tr></thead><tbody><tr><th>American Express</th><td title=\"string (4)\">amex</td><td title=\"string (2)\">15</td><td title=\"string (5)\">34,37</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>China UnionPay</th><td title=\"string (8)\">unionpay</td><td title=\"string (11)\">16,17,18,19</td><td title=\"string (2)\">62</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>Dankort</th><td title=\"string (7)\">dankort</td><td title=\"string (2)\">16</td><td title=\"string (16)\">5019,4175,4571,4</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>DinersClub</th><td title=\"string (10)\">dinersclub</td><td title=\"string (5)\">14,16</td><td title=\"string (42)\">300,301,302,303,304,305,309,36,38,39,54,55</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>DinersClub CarteBlanche</th><td title=\"string (12)\">carteblanche</td><td title=\"string (2)\">14</td><td title=\"string (23)\">300,301,302,303,304,305</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>Discover Card</th><td title=\"string (8)\">discover</td><td title=\"string (5)\">16,19</td><td title=\"string (35)\">6011,622,644,645,656,647,648,649,65</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>InterPayment</th><td title=\"string (12)\">interpayment</td><td title=\"string (11)\">16,17,18,19</td><td title=\"string (1)\">4</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>JCB</th><td title=\"string (3)\">jcb</td><td title=\"string (11)\">16,17,18,19</td><td title=\"string (27)\">352,353,354,355,356,357,358</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>Maestro</th><td title=\"string (7)\">maestro</td><td title=\"string (20)\">12,13,14,15,16,18,19</td><td title=\"string (44)\">50,56,57,58,59,60,61,62,63,64,65,66,67,68,69</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>MasterCard</th><td title=\"string (10)\">mastercard</td><td title=\"string (2)\">16</td><td title=\"string (32)\">51,52,53,54,55,22,23,24,25,26,27</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>NSPK MIR</th><td title=\"string (3)\">mir</td><td title=\"string (2)\">16</td><td title=\"string (24)\">2200,2201,2202,2203,2204</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>Troy</th><td title=\"string (4)\">troy</td><td title=\"string (2)\">16</td><td title=\"string (13)\">979200,979289</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>UATP</th><td title=\"string (4)\">uatp</td><td title=\"string (2)\">15</td><td title=\"string (1)\">1</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>Verve</th><td title=\"string (5)\">verve</td><td title=\"string (5)\">16,19</td><td title=\"string (7)\">506,650</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>Visa</th><td title=\"string (4)\">visa</td><td title=\"string (8)\">13,16,19</td><td title=\"string (1)\">4</td><td title=\"boolean\"><var>true</var></td></tr><tr><th>BMO ABM Card</th><td title=\"string (6)\">bmoabm</td><td title=\"string (2)\">16</td><td title=\"string (3)\">500</td><td title=\"boolean\"><var>false</var></td></tr><tr><th>CIBC Convenience Card</th><td title=\"string (4)\">cibc</td><td title=\"string (2)\">16</td><td title=\"string (4)\">4506</td><td title=\"boolean\"><var>false</var></td></tr><tr><th>HSBC Canada Card</th><td title=\"string (4)\">hsbc</td><td title=\"string (2)\">16</td><td title=\"string (2)\">56</td><td title=\"boolean\"><var>false</var></td></tr><tr><th>Royal Bank of Canada Client Card</th><td title=\"string (3)\">rbc</td><td title=\"string (2)\">16</td><td title=\"string (2)\">45</td><td title=\"boolean\"><var>false</var></td></tr><tr><th>Scotiabank Scotia Card</th><td title=\"string (6)\">scotia</td><td title=\"string (2)\">16</td><td title=\"string (4)\">4536</td><td title=\"boolean\"><var>false</var></td></tr><tr><th>TD Canada Trust Access Card</th><td title=\"string (7)\">tdtrust</td><td title=\"string (2)\">16</td><td title=\"string (6)\">589297</td><td title=\"boolean\"><var>false</var></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><nav></nav><dfn>American Express</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (4) \"amex\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"15\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (5) \"34,37\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>China UnionPay</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (8) \"unionpay\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (11) \"16,17,18,19\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (2) \"62\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Dankort</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (7) \"dankort\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (16) \"5019,4175,4571,4\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>DinersClub</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (10) \"dinersclub\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (5) \"14,16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (42) \"300,301,302,303,304,305,309,36,38,39,54,55\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>DinersClub CarteBlanche</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (12) \"carteblanche\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"14\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (23) \"300,301,302,303,304,305\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Discover Card</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (8) \"discover\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (5) \"16,19\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (35) \"6011,622,644,645,656,647,648,649,65\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>InterPayment</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (12) \"interpayment\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (11) \"16,17,18,19\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (1) \"4\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>JCB</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (3) \"jcb\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (11) \"16,17,18,19\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (27) \"352,353,354,355,356,357,358\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Maestro</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (7) \"maestro\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (20) \"12,13,14,15,16,18,19\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (44) \"50,56,57,58,59,60,61,62,63,64,65,66,67,68,69\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>MasterCard</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (10) \"mastercard\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (32) \"51,52,53,54,55,22,23,24,25,26,27\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>NSPK MIR</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (3) \"mir\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (24) \"2200,2201,2202,2203,2204\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Troy</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (4) \"troy\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (13) \"979200,979289\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>UATP</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (4) \"uatp\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"15\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (1) \"1\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Verve</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (5) \"verve\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (5) \"16,19\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (7) \"506,650\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Visa</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (4) \"visa\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (8) \"13,16,19\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (1) \"4\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> true</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>BMO ABM Card</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (6) \"bmoabm\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (3) \"500\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>CIBC Convenience Card</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (4) \"cibc\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (4) \"4506\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>HSBC Canada Card</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (4) \"hsbc\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (2) \"56\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Royal Bank of Canada Client Card</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (3) \"rbc\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (2) \"45\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Scotiabank Scotia Card</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (6) \"scotia\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (4) \"4536\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>TD Canada Trust Access Card</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>name</dfn> =&gt; <var>string</var> (7) \"tdtrust\"</dt></dl><dl><dt><dfn>length</dfn> =&gt; <var>string</var> (2) \"16\"</dt></dl><dl><dt><dfn>prefixes</dfn> =&gt; <var>string</var> (6) \"589297\"</dt></dl><dl><dt><dfn>checkdigit</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl></li></ul></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_cc_number(?string $ccNumber, string $type)</dfn>: <var>bool</var> Verifies that a credit card number is valid and matches the known formats for...</dt><dd><pre>/**\n * Verifies that a credit card number is valid and matches the known\n * formats for a wide number of credit card types. This does not verify\n * that the card is a valid card, only that the number is formatted correctly.\n *\n * Example:\n *  $rules = [\n *      'cc_num' =&gt; 'valid_cc_number[visa]'\n *  ];\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/CreditCardRules.php:176</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>isValidLuhn(?string $number = null)</dfn>: <var>bool</var> Checks the given number to see if the number passing a Luhn check.</dt><dd><pre>/**\n * Checks the given number to see if the number passing a Luhn check.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/CreditCardRules.php:243</small></pre></dd></dl></li></ul></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/CreditCardRules.php:30</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>valid_cc_number($ccNumber, string $type)</dfn>: <var>bool</var> Verifies that a credit card number is valid and matches the known formats for...</dt><dd><pre>/**\n * Verifies that a credit card number is valid and matches the known\n * formats for a wide number of credit card types. This does not verify\n * that the card is a valid card, only that the number is formatted correctly.\n *\n * Example:\n *  $rules = [\n *      'cc_num' =&gt; 'valid_cc_number[visa]'\n *  ];\n *\n * @param array|bool|float|int|object|string|null $ccNumber\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/StrictRules/CreditCardRules.php:47</small></pre></dd></dl></li></ul></dd></dl></dd></dl><dl><dt><var>protected</var> <dfn>rules</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>data</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>validated</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>errors</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>customErrors</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>config</dfn> -&gt; <var>Config\\Validation</var>#80 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (4)</li><li>Static methods (3)</li><li>Static properties (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>ruleSets</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (40) \"CodeIgniter\\Validation\\StrictRules\\Rules\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (46) \"CodeIgniter\\Validation\\StrictRules\\FormatRules\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (44) \"CodeIgniter\\Validation\\StrictRules\\FileRules\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (50) \"CodeIgniter\\Validation\\StrictRules\\CreditCardRules\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>templates</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>list</dfn> =&gt; <var>string</var> (33) \"CodeIgniter\\Validation\\Views\\list\"</dt></dl><dl><dt><dfn>single</dfn> =&gt; <var>string</var> (35) \"CodeIgniter\\Validation\\Views\\single\"</dt></dl></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn> Will attempt to get environment variables with names that match the propertie...</dt><dd><pre>/**\n * Will attempt to get environment variables with names\n * that match the properties of the child class.\n *\n * The \"shortPrefix\" is the lowercase-only config class name.\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:114</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>initEnvValue(&amp;$property, string $name, string $prefix, string $shortPrefix)</dfn>: <var>void</var> Initialization an environment-specific configuration setting</dt><dd><pre>/**\n * Initialization an environment-specific configuration setting\n *\n * @param array|bool|float|int|string|null $property\n *\n * @return void\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:151</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getEnvValue(string $property, string $prefix, string $shortPrefix)</dfn>: <var>string|null</var> Retrieve an environment-specific configuration setting</dt><dd><pre>/**\n * Retrieve an environment-specific configuration setting\n *\n * @return string|null\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>registerProperties()</dfn>: <var>void</var> Provides external libraries a simple way to register one or more options into...</dt><dd><pre>/**\n * Provides external libraries a simple way to register one or more\n * options into a config file.\n *\n * @return void\n *\n * @throws ReflectionException\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:237</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:72</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::setModules(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::reset()</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:100</small></pre></dd></dl></li><li><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrars</dfn> :: <var>array</var> (0)</dt></dl><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$override</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$didDiscovery</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$discovering</dfn> :: <var>boolean</var> false</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrarFile</dfn> :: <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$moduleConfig</dfn> :: <var>Config\\Modules</var>#7 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (2)</li><li>Static methods (1)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>enabled</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>public</var> <dfn>discoverInComposer</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>aliases</dfn> -&gt; <var>array</var> (5)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (6) \"events\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"filters\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (10) \"registrars\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (6) \"routes\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (8) \"services\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>composerPackages</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:46</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>shouldDiscover(string $alias)</dfn>: <var>bool</var> Should the application auto-discover the requested resource.</dt><dd><pre>/**\n * Should the application auto-discover the requested resource.\n */\n\n<small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:54</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Modules\\Modules::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:63</small></pre></dd></dl></li></ul></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>view</dfn> -&gt; <var>CodeIgniter\\View\\View</var>#83 (14)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (14)</li><li>Methods (17)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>data</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>title</dfn> =&gt; <var>string</var> (13) \"Compose Email\"</dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><dfn>validation</dfn> =&gt; <var>CodeIgniter\\Validation\\Validation</var>#81 <var>Recursion</var></dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>tempData</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>viewPath</dfn> -&gt; <var>string</var> (44) \"C:\\xampp\\htdocs\\codei46\\app\\Config/../Views\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 15 04:59 C:\\xampp\\htdocs\\codei46\\app\\Views\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>renderVars</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>start</dfn> =&gt; <var>double</var> **********.8297</dt></dl><dl><dt><dfn>view</dfn> =&gt; <var>string</var> (36) \"email_sender/email_sender_create.php\"</dt></dl><dl><dt><dfn>options</dfn> =&gt; <var>array</var> (0)</dt></dl><dl><dt><dfn>file</dfn> =&gt; <var>string</var> (52) \"1 APPPATH\\Views\\email_sender\\email_sender_create.php\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>loader</dfn> -&gt; <var>CodeIgniter\\Autoloader\\FileLocator</var>#5 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (10)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>autoloader</dfn> -&gt; <var>CodeIgniter\\Autoloader\\Autoloader</var>#2 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (18)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>prefixes</dfn> -&gt; <var>array</var> (3)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>CodeIgniter</dfn> =&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>string</var> (31) \"C:\\xampp\\htdocs\\codei46\\system\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Mar 28 03:29 C:\\xampp\\htdocs\\codei46\\system\n</pre></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Config</dfn> =&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>string</var> (35) \"C:\\xampp\\htdocs\\codei46\\app\\Config\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Mar 28 06:38 C:\\xampp\\htdocs\\codei46\\app\\Config\n</pre></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>App</dfn> =&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>string</var> (28) \"C:\\xampp\\htdocs\\codei46\\app\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Mar 28 03:29 C:\\xampp\\htdocs\\codei46\\app\n</pre></li></ul></dd></dl></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>classmap</dfn> -&gt; <var>array</var> (12)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\AbstractLogger</dfn> =&gt; <var>string</var> (68) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/AbstractLogger.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (414B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/AbstractLogger.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\InvalidArgumentException</dfn> =&gt; <var>string</var> (78) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/InvalidArgumentException.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (96B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/InvalidArgumentException.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerAwareInterface</dfn> =&gt; <var>string</var> (74) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerAwareInterface.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (231B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerAwareInterface.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerAwareTrait</dfn> =&gt; <var>string</var> (70) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerAwareTrait.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (347B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerAwareTrait.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerInterface</dfn> =&gt; <var>string</var> (69) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerInterface.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (2.7KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerInterface.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerTrait</dfn> =&gt; <var>string</var> (65) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerTrait.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (2.7KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LoggerTrait.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LogLevel</dfn> =&gt; <var>string</var> (62) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LogLevel.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (336B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/LogLevel.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\NullLogger</dfn> =&gt; <var>string</var> (64) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/NullLogger.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (643B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/PSR/Log/NullLogger.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Exception\\ExceptionInterface</dfn> =&gt; <var>string</var> (82) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/ExceptionInterfa...</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (138B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/ExceptionInterface.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/ExceptionInterface.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Exception\\InvalidArgumentException</dfn> =&gt; <var>string</var> (88) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/InvalidArgumentE...</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (212B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/InvalidArgumentException.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/InvalidArgumentException.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Exception\\RuntimeException</dfn> =&gt; <var>string</var> (80) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/RuntimeException...</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (196B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/RuntimeException.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Exception/RuntimeException.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Escaper</dfn> =&gt; <var>string</var> (61) \"C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Escaper.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (12.4KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Jan 19 18:31 C:\\xampp\\htdocs\\codei46\\system\\ThirdParty/Escaper/Escaper.php\n</pre></li></ul></dd></dl></dd></dl><dl><dt><var>protected</var> <dfn>files</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>helpers</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (3) \"url\"</dt></dl></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>initialize(Config\\Autoload $config, Config\\Modules $modules)</dfn>: <var>$this</var> Reads in the configuration array (described above) and stores the valid parts...</dt><dd><pre>/**\n * Reads in the configuration array (described above) and stores\n * the valid parts that we'll need.\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:102</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>loadComposerAutoloader(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:137</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>register()</dfn>: <var>void</var> Register the loader with the SPL autoloader stack.</dt><dd><pre>/**\n * Register the loader with the SPL autoloader stack.\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:162</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>unregister()</dfn>: <var>void</var> Unregister autoloader.</dt><dd><pre>/**\n * Unregister autoloader.\n *\n * This method is for testing.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addNamespace($namespace, ?string $path = null)</dfn>: <var>$this</var> Registers namespaces with the autoloader.</dt><dd><pre>/**\n * Registers namespaces with the autoloader.\n *\n * @param array&lt;string, list&lt;string&gt;|string&gt;|string $namespace\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:194</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getNamespace(?string $prefix = null)</dfn>: <var>array&lt;string, list&lt;string&gt;&gt;|list&lt;string&gt;</var> Get namespaces with prefixes as keys and paths as values.</dt><dd><pre>/**\n * Get namespaces with prefixes as keys and paths as values.\n *\n * If a prefix param is set, returns only paths to the given prefix.\n *\n * @return         array&lt;string, list&lt;string&gt;&gt;|list&lt;string&gt;\n * @phpstan-return ($prefix is null ? array&lt;string, list&lt;string&gt;&gt; : list&lt;string&gt;)\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:225</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>removeNamespace(string $namespace)</dfn>: <var>$this</var> Removes a single namespace from the psr4 settings.</dt><dd><pre>/**\n * Removes a single namespace from the psr4 settings.\n *\n * @return $this\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:239</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>loadClassmap(string $class)</dfn>: <var>void</var> Load a class using available class mapping.</dt><dd><pre>/**\n * Load a class using available class mapping.\n *\n * @internal For `spl_autoload_register` use.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:253</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>loadClass(string $class)</dfn>: <var>void</var> Loads the class file for a given class name.</dt><dd><pre>/**\n * Loads the class file for a given class name.\n *\n * @internal For `spl_autoload_register` use.\n *\n * @param string $class The fully qualified class name.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:269</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>loadInNamespace(string $class)</dfn>: <var>false|string The mapped file name on success, or boolean false on fail</var> Loads the class file for a given class name.</dt><dd><pre>/**\n * Loads the class file for a given class name.\n *\n * @param string $class The fully-qualified class name\n *\n * @return false|string The mapped file name on success, or boolean false on fail\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:281</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>includeFile(string $file)</dfn>: <var>false|string The filename on success, false if the file is not loaded</var> A central way to include a file. Split out primarily for testing purposes.</dt><dd><pre>/**\n * A central way to include a file. Split out primarily for testing purposes.\n *\n * @return false|string The filename on success, false if the file is not loaded\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:313</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>sanitizeFilename(string $filename)</dfn>: <var>string</var> Check file path.</dt><dd><pre>/**\n * Check file path.\n *\n * Checks special characters that are illegal in filenames on certain\n * operating systems and special characters requiring special escaping\n * to manipulate at the command line. Replaces spaces and consecutive\n * dashes with a single dash. Trim period, dash and underscore from beginning\n * and end of filename.\n *\n * @return string The sanitized filename\n *\n * @deprecated No longer used. See https://github.com/codeigniter4/CodeIgniter4/issues/7055\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:337</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>loadComposerNamespaces(Composer\\Autoload\\ClassLoader $composer, array $composerPackages)</dfn>: <var>void</var></dt><dd><pre>/**\n * @param array{only?: list&lt;string&gt;, exclude?: list&lt;string&gt;} $composerPackages\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:372</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>discoverComposerNamespaces()</dfn>: <var>void</var> Locates autoload information from Composer, if available.</dt><dd><pre>/**\n * Locates autoload information from Composer, if available.\n *\n * @deprecated No longer used.\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:453</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>loadHelpers()</dfn>: <var>void</var> Loads helpers</dt><dd><pre>/**\n * Loads helpers\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:487</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>initializeKint(bool $debug = false)</dfn>: <var>void</var> Initializes Kint</dt><dd><pre>/**\n * Initializes Kint\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:495</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>autoloadKint()</dfn>: <var>void</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:508</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>configureKint()</dfn>: <var>void</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/Autoloader.php:530</small></pre></dd></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>invalidClassnames</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(CodeIgniter\\Autoloader\\Autoloader $autoloader)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:38</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>locateFile(string $file, ?string $folder = null, string $ext = 'php')</dfn>: <var>false|string The path to the file, or false if not found.</var> Attempts to locate a file by examining the name for a namespace and looking t...</dt><dd><pre>/**\n * Attempts to locate a file by examining the name for a namespace\n * and looking through the PSR-4 namespaced files that we know about.\n *\n * @param string                $file   The relative file path or namespaced file to\n *                                      locate. If not namespaced, search in the app\n *                                      folder.\n * @param non-empty-string|null $folder The folder within the namespace that we should\n *                                      look for the file. If $file does not contain\n *                                      this value, it will be appended to the namespace\n *                                      folder.\n * @param string                $ext    The file extension the file should have.\n *\n * @return false|string The path to the file, or false if not found.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:58</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getClassname(string $file)</dfn>: <var>string</var> Examines a file and returns the fully qualified class name.</dt><dd><pre>/**\n * Examines a file and returns the fully qualified class name.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:129</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>search(string $path, string $ext = 'php', bool $prioritizeApp = true)</dfn>: <var>array</var> Searches through all of the defined namespaces looking for a file. Returns an...</dt><dd><pre>/**\n * Searches through all of the defined namespaces looking for a file.\n * Returns an array of all found locations for the defined file.\n *\n * Example:\n *\n *  $locator-&gt;search('Config/Routes.php');\n *  // Assuming PSR4 namespaces include foo and bar, might return:\n *  [\n *      'app/Modules/foo/Config/Routes.php',\n *      'app/Modules/bar/Config/Routes.php',\n *  ]\n *\n * @return list&lt;string&gt;\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>ensureExt(string $path, string $ext)</dfn>: <var>string</var> Ensures a extension is at the end of a filename</dt><dd><pre>/**\n * Ensures a extension is at the end of a filename\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:223</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getNamespaces()</dfn>: <var>array&lt;int, array&lt;string, string&gt;&gt;</var> Return the namespace mappings we know about.</dt><dd><pre>/**\n * Return the namespace mappings we know about.\n *\n * @return array&lt;int, array&lt;string, string&gt;&gt;\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:241</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>findQualifiedNameFromPath(string $path)</dfn>: <var>false|string The qualified name or false if the path is not found</var> Find the qualified name of a file according to the namespace of the first mat...</dt><dd><pre>/**\n * Find the qualified name of a file according to\n * the namespace of the first matched namespace path.\n *\n * @return false|string The qualified name or false if the path is not found\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:275</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>listFiles(string $path)</dfn>: <var>array</var> Scans the defined namespaces, returning a list of all files that are containe...</dt><dd><pre>/**\n * Scans the defined namespaces, returning a list of all files\n * that are contained within the subpath specified by $path.\n *\n * @return list&lt;string&gt; List of file paths\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:328</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>listNamespaceFiles(string $prefix, string $path)</dfn>: <var>array</var> Scans the provided namespace, returning a list of all files that are containe...</dt><dd><pre>/**\n * Scans the provided namespace, returning a list of all files\n * that are contained within the sub path specified by $path.\n *\n * @return list&lt;string&gt; List of file paths\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:362</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>legacyLocate(string $file, ?string $folder = null)</dfn>: <var>false|string The path to the file, or false if not found.</var> Checks the app folder to see if the file can be found. Only for use with file...</dt><dd><pre>/**\n * Checks the app folder to see if the file can be found.\n * Only for use with filenames that DO NOT include namespacing.\n *\n * @param non-empty-string|null $folder\n *\n * @return false|string The path to the file, or false if not found.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Autoloader/FileLocator.php:399</small></pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>logger</dfn> -&gt; <var>CodeIgniter\\Log\\Logger</var>#78 (9)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (9)</li><li>Methods (12)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>logLevels</dfn> -&gt; <var>array</var> (8)</dt><dd><dl><dt><dfn>emergency</dfn> =&gt; <var>integer</var> 1</dt></dl><dl><dt><dfn>alert</dfn> =&gt; <var>integer</var> 2</dt></dl><dl><dt><dfn>critical</dfn> =&gt; <var>integer</var> 3</dt></dl><dl><dt><dfn>error</dfn> =&gt; <var>integer</var> 4</dt></dl><dl><dt><dfn>warning</dfn> =&gt; <var>integer</var> 5</dt></dl><dl><dt><dfn>notice</dfn> =&gt; <var>integer</var> 6</dt></dl><dl><dt><dfn>info</dfn> =&gt; <var>integer</var> 7</dt></dl><dl><dt><dfn>debug</dfn> =&gt; <var>integer</var> 8</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>loggableLevels</dfn> -&gt; <var>array</var> (9)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (9) \"emergency\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (5) \"alert\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (8) \"critical\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (5) \"error\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl><dl><dt><dfn>5</dfn> =&gt; <var>string</var> (6) \"notice\"</dt></dl><dl><dt><dfn>6</dfn> =&gt; <var>string</var> (4) \"info\"</dt></dl><dl><dt><dfn>7</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt><dfn>8</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>filePermissions</dfn> -&gt; <var>integer</var> 420</dt></dl><dl><dt><var>protected</var> <dfn>dateFormat</dfn> -&gt; <var>string</var> (11) \"Y-m-d H:i:s\"</dt></dl><dl><dt><var>protected</var> <dfn>fileExt</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>handlers</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>CodeIgniter\\Log\\Handlers\\FileHandler</dfn> =&gt; <var>CodeIgniter\\Log\\Handlers\\FileHandler</var>#45 (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (5)</li><li>Methods (4)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>handles</dfn> -&gt; <var>array</var> (8)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (8) \"critical\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (5) \"alert\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (9) \"emergency\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (5) \"error\"</dt></dl><dl><dt><dfn>5</dfn> =&gt; <var>string</var> (4) \"info\"</dt></dl><dl><dt><dfn>6</dfn> =&gt; <var>string</var> (6) \"notice\"</dt></dl><dl><dt><dfn>7</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>dateFormat</dfn> -&gt; <var>string</var> (11) \"Y-m-d H:i:s\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (38) \"C:\\xampp\\htdocs\\codei46\\writable\\logs/\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 15 05:04 C:\\xampp\\htdocs\\codei46\\writable\\logs\n</pre></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>fileExtension</dfn> -&gt; <var>string</var> (3) \"log\"</dt></dl><dl><dt><var>protected</var> <dfn>filePermissions</dfn> -&gt; <var>integer</var> 420</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(array $config = array())</dfn> Constructor</dt><dd><pre>/**\n * Constructor\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Handlers/FileHandler.php:50</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>handle($level, $message)</dfn>: <var>bool</var> Handles logging the message. If the handler returns false, then execution of ...</dt><dd><pre>/**\n * Handles logging the message.\n * If the handler returns false, then execution of handlers\n * will stop. Any handlers that have not run, yet, will not\n * be run.\n *\n * @param string $level\n * @param string $message\n *\n * @throws Exception\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Handlers/FileHandler.php:73</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>canHandle(string $level)</dfn>: <var>bool</var> Checks whether the Handler will handle logging items of this log Level.</dt><dd><pre>/**\n * Checks whether the Handler will handle logging items of this\n * log Level.\n */\n\n<small>Inherited from CodeIgniter\\Log\\Handlers\\BaseHandler\nDefined in &lt;ROOT&gt;/codei46/system/Log/Handlers/BaseHandler.php:47</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setDateFormat(string $format)</dfn>: <var>CodeIgniter\\Log\\Handlers\\HandlerInterface</var> Stores the date format to use while logging messages.</dt><dd><pre>/**\n * Stores the date format to use while logging messages.\n */\n\n<small>Inherited from CodeIgniter\\Log\\Handlers\\BaseHandler\nDefined in &lt;ROOT&gt;/codei46/system/Log/Handlers/BaseHandler.php:55</small></pre></dd></dl></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>handlerConfig</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>CodeIgniter\\Log\\Handlers\\FileHandler</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>handles</dfn> =&gt; <var>array</var> (8)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (8) \"critical\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (5) \"alert\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (9) \"emergency\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (5) \"error\"</dt></dl><dl><dt><dfn>5</dfn> =&gt; <var>string</var> (4) \"info\"</dt></dl><dl><dt><dfn>6</dfn> =&gt; <var>string</var> (6) \"notice\"</dt></dl><dl><dt><dfn>7</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl></dd></dl><dl><dt><dfn>fileExtension</dfn> =&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><dfn>filePermissions</dfn> =&gt; <var>integer</var> 420</dt></dl><dl><dt><dfn>path</dfn> =&gt; <var>string</var> (0) \"\"</dt></dl></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>logCache</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>level</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>msg</dfn> =&gt; <var>string</var> (83) \"Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' ...</dt><dd><pre>Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver.\n</pre></dd></dl></dd></dl></dd></dl><dl><dt><var>protected</var> <dfn>cacheLogs</dfn> -&gt; <var>boolean</var> true</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct($config, bool $debug = true)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param \\Config\\Logger $config\n *\n * @throws RuntimeException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:124</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>emergency(Stringable|string $message, array $context = array())</dfn>: <var>void</var> System is unusable.</dt><dd><pre>/**\n * System is unusable.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:162</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alert(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Action must be taken immediately.</dt><dd><pre>/**\n * Action must be taken immediately.\n *\n * Example: Entire website down, database unavailable, etc. This should\n * trigger the SMS alerts and wake you up.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:175</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>critical(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Critical conditions.</dt><dd><pre>/**\n * Critical conditions.\n *\n * Example: Application component unavailable, unexpected exception.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:187</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>error(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Runtime errors that do not require immediate action but should typically be l...</dt><dd><pre>/**\n * Runtime errors that do not require immediate action but should typically\n * be logged and monitored.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:198</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>warning(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Exceptional occurrences that are not errors.</dt><dd><pre>/**\n * Exceptional occurrences that are not errors.\n *\n * Example: Use of deprecated APIs, poor use of an API, undesirable things\n * that are not necessarily wrong.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:211</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>notice(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Normal but significant events.</dt><dd><pre>/**\n * Normal but significant events.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:221</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>info(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Interesting events.</dt><dd><pre>/**\n * Interesting events.\n *\n * Example: User logs in, SQL logs.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:233</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>debug(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Detailed debug information.</dt><dd><pre>/**\n * Detailed debug information.\n *\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:243</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>log($level, Stringable|string $message, array $context = array())</dfn>: <var>void</var> Logs with an arbitrary level.</dt><dd><pre>/**\n * Logs with an arbitrary level.\n *\n * @param string $level\n * @param string $message\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>interpolate($message, array $context = array())</dfn>: <var>string</var> Replaces any placeholders in the message with variables from the context, as ...</dt><dd><pre>/**\n * Replaces any placeholders in the message with variables\n * from the context, as well as a few special items like:\n *\n * {session_vars}\n * {post_vars}\n * {get_vars}\n * {env}\n * {env:foo}\n * {file}\n * {line}\n *\n * @param string $message\n *\n * @return string\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>determineFile()</dfn>: <var>array</var> Determines the file and line that the logging call was made from by analyzing...</dt><dd><pre>/**\n * Determines the file and line that the logging call\n * was made from by analyzing the backtrace.\n * Find the earliest stack frame that is part of our logging system.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Log/Logger.php:374</small></pre></dd></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>debug</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>performanceData</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (3)</dt><dd><dl><dt><dfn>start</dfn> =&gt; <var>double</var> **********.8297</dt></dl><dl><dt><dfn>end</dfn> =&gt; <var>double</var> **********.8641</dt></dl><dl><dt><dfn>view</dfn> =&gt; <var>string</var> (36) \"email_sender/email_sender_create.php\"</dt></dl></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>config</dfn> -&gt; <var>Config\\View</var>#82 (6)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (6)</li><li>Methods (4)</li><li>Static methods (3)</li><li>Static properties (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>saveData</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>filters</dfn> -&gt; <var>array</var> (21)</dt><dd><dl><dt><dfn>abs</dfn> =&gt; <var>string</var> (4) \"\\abs\"</dt></dl><dl><dt><dfn>capitalize</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Filters::capitalize\"</dt></dl><dl><dt><dfn>date</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Filters::date\"</dt></dl><dl><dt><dfn>date_modify</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::date_modify\"</dt></dl><dl><dt><dfn>default</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::default\"</dt></dl><dl><dt><dfn>esc</dfn> =&gt; <var>string</var> (30) \"\\CodeIgniter\\View\\Filters::esc\"</dt></dl><dl><dt><dfn>excerpt</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::excerpt\"</dt></dl><dl><dt><dfn>highlight</dfn> =&gt; <var>string</var> (36) \"\\CodeIgniter\\View\\Filters::highlight\"</dt></dl><dl><dt><dfn>highlight_code</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::highlight_code\"</dt></dl><dl><dt><dfn>limit_words</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_words\"</dt></dl><dl><dt><dfn>limit_chars</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_chars\"</dt></dl><dl><dt><dfn>local_currency</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::local_currency\"</dt></dl><dl><dt><dfn>local_number</dfn> =&gt; <var>string</var> (39) \"\\CodeIgniter\\View\\Filters::local_number\"</dt></dl><dl><dt><dfn>lower</dfn> =&gt; <var>string</var> (11) \"\\strtolower\"</dt></dl><dl><dt><dfn>nl2br</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::nl2br\"</dt></dl><dl><dt><dfn>number_format</dfn> =&gt; <var>string</var> (14) \"\\number_format\"</dt></dl><dl><dt><dfn>prose</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::prose\"</dt></dl><dl><dt><dfn>round</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::round\"</dt></dl><dl><dt><dfn>strip_tags</dfn> =&gt; <var>string</var> (11) \"\\strip_tags\"</dt></dl><dl><dt><dfn>title</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::title\"</dt></dl><dl><dt><dfn>upper</dfn> =&gt; <var>string</var> (11) \"\\strtoupper\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>plugins</dfn> -&gt; <var>array</var> (10)</dt><dd><dl><dt><dfn>csp_script_nonce</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Plugins::cspScriptNonce\"</dt></dl><dl><dt><dfn>csp_style_nonce</dfn> =&gt; <var>string</var> (40) \"\\CodeIgniter\\View\\Plugins::cspStyleNonce\"</dt></dl><dl><dt><dfn>current_url</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::currentURL\"</dt></dl><dl><dt><dfn>previous_url</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Plugins::previousURL\"</dt></dl><dl><dt><dfn>mailto</dfn> =&gt; <var>string</var> (33) \"\\CodeIgniter\\View\\Plugins::mailto\"</dt></dl><dl><dt><dfn>safe_mailto</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::safeMailto\"</dt></dl><dl><dt><dfn>lang</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Plugins::lang\"</dt></dl><dl><dt><dfn>validation_errors</dfn> =&gt; <var>string</var> (43) \"\\CodeIgniter\\View\\Plugins::validationErrors\"</dt></dl><dl><dt><dfn>route</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Plugins::route\"</dt></dl><dl><dt><dfn>siteURL</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Plugins::siteURL\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>coreFilters</dfn> -&gt; <var>array</var> (21)</dt><dd><dl><dt><dfn>abs</dfn> =&gt; <var>string</var> (4) \"\\abs\"</dt></dl><dl><dt><dfn>capitalize</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Filters::capitalize\"</dt></dl><dl><dt><dfn>date</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Filters::date\"</dt></dl><dl><dt><dfn>date_modify</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::date_modify\"</dt></dl><dl><dt><dfn>default</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::default\"</dt></dl><dl><dt><dfn>esc</dfn> =&gt; <var>string</var> (30) \"\\CodeIgniter\\View\\Filters::esc\"</dt></dl><dl><dt><dfn>excerpt</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::excerpt\"</dt></dl><dl><dt><dfn>highlight</dfn> =&gt; <var>string</var> (36) \"\\CodeIgniter\\View\\Filters::highlight\"</dt></dl><dl><dt><dfn>highlight_code</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::highlight_code\"</dt></dl><dl><dt><dfn>limit_words</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_words\"</dt></dl><dl><dt><dfn>limit_chars</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_chars\"</dt></dl><dl><dt><dfn>local_currency</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::local_currency\"</dt></dl><dl><dt><dfn>local_number</dfn> =&gt; <var>string</var> (39) \"\\CodeIgniter\\View\\Filters::local_number\"</dt></dl><dl><dt><dfn>lower</dfn> =&gt; <var>string</var> (11) \"\\strtolower\"</dt></dl><dl><dt><dfn>nl2br</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::nl2br\"</dt></dl><dl><dt><dfn>number_format</dfn> =&gt; <var>string</var> (14) \"\\number_format\"</dt></dl><dl><dt><dfn>prose</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::prose\"</dt></dl><dl><dt><dfn>round</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::round\"</dt></dl><dl><dt><dfn>strip_tags</dfn> =&gt; <var>string</var> (11) \"\\strip_tags\"</dt></dl><dl><dt><dfn>title</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::title\"</dt></dl><dl><dt><dfn>upper</dfn> =&gt; <var>string</var> (11) \"\\strtoupper\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>corePlugins</dfn> -&gt; <var>array</var> (10)</dt><dd><dl><dt><dfn>csp_script_nonce</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Plugins::cspScriptNonce\"</dt></dl><dl><dt><dfn>csp_style_nonce</dfn> =&gt; <var>string</var> (40) \"\\CodeIgniter\\View\\Plugins::cspStyleNonce\"</dt></dl><dl><dt><dfn>current_url</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::currentURL\"</dt></dl><dl><dt><dfn>previous_url</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Plugins::previousURL\"</dt></dl><dl><dt><dfn>mailto</dfn> =&gt; <var>string</var> (33) \"\\CodeIgniter\\View\\Plugins::mailto\"</dt></dl><dl><dt><dfn>safe_mailto</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::safeMailto\"</dt></dl><dl><dt><dfn>lang</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Plugins::lang\"</dt></dl><dl><dt><dfn>validation_errors</dfn> =&gt; <var>string</var> (43) \"\\CodeIgniter\\View\\Plugins::validationErrors\"</dt></dl><dl><dt><dfn>route</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Plugins::route\"</dt></dl><dl><dt><dfn>siteURL</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Plugins::siteURL\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>decorators</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn> Merge the built-in and developer-configured filters and plugins, with prefere...</dt><dd><pre>/**\n * Merge the built-in and developer-configured filters and plugins,\n * with preference to the developer ones.\n */\n\n<small>Inherited from CodeIgniter\\Config\\View\nDefined in &lt;ROOT&gt;/codei46/system/Config/View.php:129</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>initEnvValue(&amp;$property, string $name, string $prefix, string $shortPrefix)</dfn>: <var>void</var> Initialization an environment-specific configuration setting</dt><dd><pre>/**\n * Initialization an environment-specific configuration setting\n *\n * @param array|bool|float|int|string|null $property\n *\n * @return void\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:151</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getEnvValue(string $property, string $prefix, string $shortPrefix)</dfn>: <var>string|null</var> Retrieve an environment-specific configuration setting</dt><dd><pre>/**\n * Retrieve an environment-specific configuration setting\n *\n * @return string|null\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>registerProperties()</dfn>: <var>void</var> Provides external libraries a simple way to register one or more options into...</dt><dd><pre>/**\n * Provides external libraries a simple way to register one or more\n * options into a config file.\n *\n * @return void\n *\n * @throws ReflectionException\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:237</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:72</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::setModules(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::reset()</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Config/BaseConfig.php:100</small></pre></dd></dl></li><li><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrars</dfn> :: <var>array</var> (0)</dt></dl><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$override</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$didDiscovery</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$discovering</dfn> :: <var>boolean</var> false</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrarFile</dfn> :: <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$moduleConfig</dfn> :: <var>Config\\Modules</var>#7 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (2)</li><li>Static methods (1)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>enabled</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>public</var> <dfn>discoverInComposer</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>aliases</dfn> -&gt; <var>array</var> (5)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (6) \"events\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"filters\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (10) \"registrars\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (6) \"routes\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (8) \"services\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>composerPackages</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:46</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>shouldDiscover(string $alias)</dfn>: <var>bool</var> Should the application auto-discover the requested resource.</dt><dd><pre>/**\n * Should the application auto-discover the requested resource.\n */\n\n<small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:54</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Modules\\Modules::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/Modules/Modules.php:63</small></pre></dd></dl></li></ul></dd></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>saveData</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>protected</var> <dfn>viewsCount</dfn> -&gt; <var>integer</var> 1</dt></dl><dl><dt><var>protected</var> <dfn>layout</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>sections</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>sectionStack</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\View $config, ?string $viewPath = null, ?CodeIgniter\\Autoloader\\FileLocatorInterface $loader = null, ?bool $debug = null, ?Psr\\Log\\LoggerInterface $logger = null)</dfn></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:135</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>render(string $view, ?array $options = null, ?bool $saveData = null)</dfn>: <var>string</var> Builds the output based upon a file name and any data that has already been set.</dt><dd><pre>/**\n * Builds the output based upon a file name and any\n * data that has already been set.\n *\n * Valid $options:\n *  - cache      Number of seconds to cache for\n *  - cache_name Name to use for cache\n *\n * @param string                    $view     File name of the view source\n * @param array&lt;string, mixed&gt;|null $options  Reserved for 3rd-party uses since\n *                                            it might be needed to pass additional info\n *                                            to other template engines.\n * @param bool|null                 $saveData If true, saves data for subsequent calls,\n *                                            if false, cleans the data after displaying,\n *                                            if null, uses the config setting.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>renderString(string $view, ?array $options = null, ?bool $saveData = null)</dfn>: <var>string</var> Builds the output based upon a string and any data that has already been set....</dt><dd><pre>/**\n * Builds the output based upon a string and any\n * data that has already been set.\n * Cache does not apply, because there is no \"key\".\n *\n * @param string                    $view     The view contents\n * @param array&lt;string, mixed&gt;|null $options  Reserved for 3rd-party uses since\n *                                            it might be needed to pass additional info\n *                                            to other template engines.\n * @param bool|null                 $saveData If true, saves data for subsequent calls,\n *                                            if false, cleans the data after displaying,\n *                                            if null, uses the config setting.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:307</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>excerpt(string $string, int $length = 20)</dfn>: <var>string</var> Extract first bit of a long string and add ellipsis</dt><dd><pre>/**\n * Extract first bit of a long string and add ellipsis\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:330</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setData(array $data = array(), ?string $context = null)</dfn>: <var>CodeIgniter\\View\\RendererInterface</var> Sets several pieces of view data at once.</dt><dd><pre>/**\n * Sets several pieces of view data at once.\n *\n * @param         non-empty-string|null                     $context The context to escape it for.\n *                                                                   If 'raw', no escaping will happen.\n * @phpstan-param null|'html'|'js'|'css'|'url'|'attr'|'raw' $context\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:342</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setVar(string $name, $value = null, ?string $context = null)</dfn>: <var>CodeIgniter\\View\\RendererInterface</var> Sets a single piece of view data.</dt><dd><pre>/**\n * Sets a single piece of view data.\n *\n * @param         mixed                                     $value\n * @param         non-empty-string|null                     $context The context to escape it for.\n *                                                                   If 'raw', no escaping will happen.\n * @phpstan-param null|'html'|'js'|'css'|'url'|'attr'|'raw' $context\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:362</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resetData()</dfn>: <var>CodeIgniter\\View\\RendererInterface</var> Removes all of the view data from the system.</dt><dd><pre>/**\n * Removes all of the view data from the system.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:377</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getData()</dfn>: <var>array</var> Returns the current data that will be displayed in the view.</dt><dd><pre>/**\n * Returns the current data that will be displayed in the view.\n *\n * @return array&lt;string, mixed&gt;\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:389</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>extend(string $layout)</dfn>: <var>void</var> Specifies that the current view should extend an existing layout.</dt><dd><pre>/**\n * Specifies that the current view should extend an existing layout.\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:399</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>section(string $name)</dfn>: <var>void</var> Starts holds content for a section within the layout.</dt><dd><pre>/**\n * Starts holds content for a section within the layout.\n *\n * @param string $name Section name\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:411</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>endSection()</dfn>: <var>void</var> Captures the last section</dt><dd><pre>/**\n * Captures the last section\n *\n * @return void\n *\n * @throws RuntimeException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:425</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>renderSection(string $sectionName, bool $saveData = false)</dfn>: <var>string</var> Renders a section's contents.</dt><dd><pre>/**\n * Renders a section's contents.\n *\n * @param bool $saveData If true, saves data for subsequent calls,\n *                       if false, cleans the data after displaying.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:449</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>include(string $view, ?array $options = null, $saveData = true)</dfn>: <var>string</var> Used within layout views to include additional views.</dt><dd><pre>/**\n * Used within layout views to include additional views.\n *\n * @param array&lt;string, mixed&gt;|null $options\n * @param bool                      $saveData\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:473</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPerformanceData()</dfn>: <var>array</var> Returns the performance data that might have been collected during the execut...</dt><dd><pre>/**\n * Returns the performance data that might have been collected\n * during the execution. Used primarily in the Debug Toolbar.\n *\n * @return list&lt;array{start: float, end: float, view: string}&gt;\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:484</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>logPerformance(float $start, float $end, string $view)</dfn>: <var>void</var> Logs performance data for rendering a view.</dt><dd><pre>/**\n * Logs performance data for rendering a view.\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:494</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>prepareTemplateData(bool $saveData)</dfn>: <var>void</var></dt><dd><pre><small>Defined in &lt;ROOT&gt;/codei46/system/View/View.php:505</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>decorateOutput(string $html)</dfn>: <var>string</var> Runs the generated output through any declared view decorators.</dt><dd><pre>/**\n * Runs the generated output through any declared\n * view decorators.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/View/ViewDecoratorTrait.php:25</small></pre></dd></dl></li></ul></dd></dl></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>__construct($config, CodeIgniter\\View\\RendererInterface $view)</dfn> Validation constructor.<div class=\"access-path\">new \\CodeIgniter\\Validation\\Validation()</div></dt><dd><pre>/**\n * Validation constructor.\n *\n * @param ValidationConfig $config\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:115</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>run(?array $data = null, ?string $group = null, $dbGroup = null)</dfn>: <var>bool</var> Runs the validation process, returning true/false determining whether validat...<div class=\"access-path\">$value-&gt;run()</div></dt><dd><pre>/**\n * Runs the validation process, returning true/false determining whether\n * validation was successful or not.\n *\n * @param array|null                                 $data    The array of data to validate.\n * @param string|null                                $group   The predefined group of rules to apply.\n * @param array|BaseConnection|non-empty-string|null $dbGroup The database group to use.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:134</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>check($value, $rules, array $errors = array(), $dbGroup = null)</dfn>: <var>bool</var> Runs the validation process, returning true or false determining whether vali...<div class=\"access-path\">$value-&gt;check()</div></dt><dd><pre>/**\n * Runs the validation process, returning true or false determining whether\n * validation was successful or not.\n *\n * @param array|bool|float|int|object|string|null $value   The data to validate.\n * @param array|string                            $rules   The validation rules.\n * @param list&lt;string&gt;                            $errors  The custom error message.\n * @param string|null                             $dbGroup The database group to use.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:242</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getValidated()</dfn>: <var>array</var> Returns the actual validated data.<div class=\"access-path\">$value-&gt;getValidated()</div></dt><dd><pre>/**\n * Returns the actual validated data.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:261</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>processRules(string $field, ?string $label, $value, $rules = null, ?array $data = null, ?string $originalField = null)</dfn>: <var>bool</var> Runs all of $rules against $field, until one fails, or all of them have been ...</dt><dd><pre>/**\n * Runs all of $rules against $field, until one fails, or\n * all of them have been processed. If one fails, it adds\n * the error to $this-&gt;errors and moves on to the next,\n * so that we can collect all of the first errors.\n *\n * @param array|string $value\n * @param array        $rules\n * @param array        $data          The array of data to validate, with `DBGroup`.\n * @param string|null  $originalField The original asterisk field name like \"foo.*.bar\".\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:277</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>processIfExist(string $field, array $rules, array $data)</dfn>: <var>array|true The modified rules or true if we return early</var></dt><dd><pre>/**\n * @param array $data The array of data to validate, with `DBGroup`.\n *\n * @return array|true The modified rules or true if we return early\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:385</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>processPermitEmpty($value, array $rules, array $data)</dfn>: <var>array|true The modified rules or true if we return early</var></dt><dd><pre>/**\n * @param array|string $value\n * @param array        $data  The array of data to validate, with `DBGroup`.\n *\n * @return array|true The modified rules or true if we return early\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:427</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>isClosure($rule)</dfn>: <var>bool</var></dt><dd><pre>/**\n * @param Closure(bool|float|int|list&lt;mixed&gt;|object|string|null, bool|float|int|list&lt;mixed&gt;|object|string|null, string|null, string|null): (bool|string) $rule\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:471</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>isStringList(array $array)</dfn>: <var>bool</var> Is the array a string list `list&lt;string&gt;`?</dt><dd><pre>/**\n * Is the array a string list `list&lt;string&gt;`?\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:479</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>withRequest(CodeIgniter\\HTTP\\RequestInterface $request)</dfn>: <var>CodeIgniter\\Validation\\ValidationInterface</var> Takes a Request object and grabs the input data to use from its array values.<div class=\"access-path\">$value-&gt;withRequest()</div></dt><dd><pre>/**\n * Takes a Request object and grabs the input data to use from its\n * array values.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:506</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>setRule(string $field, ?string $label, $rules, array $errors = array())</dfn>: <var>$this</var> Sets (or adds) an individual rule and custom error messages for a single field.<div class=\"access-path\">$value-&gt;setRule()</div></dt><dd><pre>/**\n * Sets (or adds) an individual rule and custom error messages for a single\n * field.\n *\n * The custom error message should be just the messages that apply to\n * this field, like so:\n *    [\n *        'rule1' =&gt; 'message1',\n *        'rule2' =&gt; 'message2',\n *    ]\n *\n * @param array|string $rules  The validation rules.\n * @param array        $errors The custom error message.\n *\n * @return $this\n *\n * @throws InvalidArgumentException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:548</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>setRules(array $rules, array $errors = array())</dfn>: <var>CodeIgniter\\Validation\\ValidationInterface</var> Stores the rules that should be used to validate the items.<div class=\"access-path\">$value-&gt;setRules()</div></dt><dd><pre>/**\n * Stores the rules that should be used to validate the items.\n *\n * Rules should be an array formatted like:\n *    [\n *        'field' =&gt; 'rule1|rule2'\n *    ]\n *\n * The $errors array should be formatted like:\n *    [\n *        'field' =&gt; [\n *            'rule1' =&gt; 'message1',\n *            'rule2' =&gt; 'message2',\n *        ],\n *    ]\n *\n * @param array $errors An array of custom error messages\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:588</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getRules()</dfn>: <var>array</var> Returns all of the rules currently defined.<div class=\"access-path\">$value-&gt;getRules()</div></dt><dd><pre>/**\n * Returns all of the rules currently defined.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:623</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>hasRule(string $field)</dfn>: <var>bool</var> Checks to see if the rule for key $field has been set or not.<div class=\"access-path\">$value-&gt;hasRule()</div></dt><dd><pre>/**\n * Checks to see if the rule for key $field has been set or not.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:631</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getRuleGroup(string $group)</dfn>: <var>array</var> Get rule group.<div class=\"access-path\">$value-&gt;getRuleGroup()</div></dt><dd><pre>/**\n * Get rule group.\n *\n * @param string $group Group.\n *\n * @return list&lt;string&gt; Rule group.\n *\n * @throws ValidationException If group not found.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:645</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>setRuleGroup(string $group)</dfn>: <var>void</var> Set rule group.<div class=\"access-path\">$value-&gt;setRuleGroup()</div></dt><dd><pre>/**\n * Set rule group.\n *\n * @param string $group Group.\n *\n * @return void\n *\n * @throws ValidationException If group not found.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:667</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>listErrors(string $template = 'list')</dfn>: <var>string</var> Returns the rendered HTML of the errors as defined in $template.<div class=\"access-path\">$value-&gt;listErrors()</div></dt><dd><pre>/**\n * Returns the rendered HTML of the errors as defined in $template.\n *\n * You can also use validation_list_errors() in Form helper.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:683</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>showError(string $field, string $template = 'single')</dfn>: <var>string</var> Displays a single error in formatted HTML as defined in the $template view.<div class=\"access-path\">$value-&gt;showError()</div></dt><dd><pre>/**\n * Displays a single error in formatted HTML as defined in the $template view.\n *\n * You can also use validation_show_error() in Form helper.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:699</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>loadRuleSets()</dfn>: <var>void</var> Loads all of the rulesets classes that have been defined in the Config\\Valida...</dt><dd><pre>/**\n * Loads all of the rulesets classes that have been defined in the\n * Config\\Validation and stores them locally so we can use them.\n *\n * @return void\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:720</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>loadRuleGroup(?string $group = null)</dfn>: <var>array&lt;int, array&gt; [rules, customErrors]</var> Loads custom rule groups (if set) into the current rules.<div class=\"access-path\">$value-&gt;loadRuleGroup()</div></dt><dd><pre>/**\n * Loads custom rule groups (if set) into the current rules.\n *\n * Rules can be pre-defined in Config\\Validation and can\n * be any name, but must all still be an array of the\n * same format used with setRules(). Additionally, check\n * for {group}_errors for an array of custom error messages.\n *\n * @param non-empty-string|null $group\n *\n * @return array&lt;int, array&gt; [rules, customErrors]\n *\n * @throws ValidationException\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:745</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>fillPlaceholders(array $rules, array $data)</dfn>: <var>array</var> Replace any placeholders within the rules with the values that match the 'key...</dt><dd><pre>/**\n * Replace any placeholders within the rules with the values that\n * match the 'key' of any properties being set. For example, if\n * we had the following $data array:\n *\n * [ 'id' =&gt; 13 ]\n *\n * and the following rule:\n *\n *  'is_unique[users,email,id,{id}]'\n *\n * The value of {id} would be replaced with the actual id in the form data:\n *\n *  'is_unique[users,email,id,13]'\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:787</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>retrievePlaceholders(string $rule, array $data)</dfn>: <var>array</var> Retrieves valid placeholder fields.</dt><dd><pre>/**\n * Retrieves valid placeholder fields.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:842</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>hasError(string $field)</dfn>: <var>bool</var> Checks to see if an error exists for the given field.<div class=\"access-path\">$value-&gt;hasError()</div></dt><dd><pre>/**\n * Checks to see if an error exists for the given field.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:852</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getError(?string $field = null)</dfn>: <var>string</var> Returns the error(s) for a specified $field (or empty string if not set).<div class=\"access-path\">$value-&gt;getError()</div></dt><dd><pre>/**\n * Returns the error(s) for a specified $field (or empty string if not\n * set).\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:861</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getErrors()</dfn>: <var>array</var> Returns the array of errors that were encountered during a run() call. The ar...<div class=\"access-path\">$value-&gt;getErrors()</div></dt><dd><pre>/**\n * Returns the array of errors that were encountered during\n * a run() call. The array should be in the following format:\n *\n *    [\n *        'field1' =&gt; 'error message',\n *        'field2' =&gt; 'error message',\n *    ]\n *\n * @return array&lt;string, string&gt;\n *\n * @codeCoverageIgnore\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:889</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>setError(string $field, string $error)</dfn>: <var>CodeIgniter\\Validation\\ValidationInterface</var> Sets the error for a specific field. Used by custom validation methods.<div class=\"access-path\">$value-&gt;setError()</div></dt><dd><pre>/**\n * Sets the error for a specific field. Used by custom validation methods.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:897</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getErrorMessage(string $rule, string $field, ?string $label = null, ?string $param = null, ?string $value = null, ?string $originalField = null)</dfn>: <var>string</var> Attempts to find the appropriate error message</dt><dd><pre>/**\n * Attempts to find the appropriate error message\n *\n * @param non-empty-string|null $label\n * @param string|null           $value The value that caused the validation to fail.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:910</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>splitRules(string $rules)</dfn>: <var>array</var> Split rules string by pipe operator.</dt><dd><pre>/**\n * Split rules string by pipe operator.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:943</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>reset()</dfn>: <var>CodeIgniter\\Validation\\ValidationInterface</var> Resets the class to a blank slate. Should be called whenever you need to proc...<div class=\"access-path\">$value-&gt;reset()</div></dt><dd><pre>/**\n * Resets the class to a blank slate. Should be called whenever\n * you need to process more than one array.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:985</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>private static</var> <dfn>CodeIgniter\\Validation\\Validation::getRegex(string $field)</dfn>: <var>string</var> Returns regex pattern for key with dot array syntax.</dt><dd><pre>/**\n * Returns regex pattern for key with dot array syntax.\n */\n\n<small>Defined in &lt;ROOT&gt;/codei46/system/Validation/Validation.php:222</small></pre></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost/codei46/email-sender/new"}, "headers": {"Host": "localhost", "Connection": "keep-alive", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost/codei46/email-sender", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "csrf_cookie_name=2dad20e4de25624936e6bca004bb1ba1; ci_session=e0977661fad7aee083577aa7804c3fbf"}, "cookies": {"csrf_cookie_name": "2dad20e4de25624936e6bca004bb1ba1", "ci_session": "e0977661fad7aee083577aa7804c3fbf"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://localhost/codei46/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}