<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .app-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
        }
        
        .app-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            text-decoration: none;
            color: inherit;
        }
        
        .app-icon {
            font-size: 4rem;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .app-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .app-description {
            color: #666;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .app-features {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .app-features li {
            color: #555;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .app-features li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .app-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-coming-soon {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 50px;
        }
        
        .footer p {
            margin-bottom: 10px;
        }
        
        .system-info {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
        }
        
        .system-info h3 {
            color: white;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .system-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            color: white;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .apps-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .app-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Apps Dashboard</h1>
            <p>Welcome to your application management center</p>
        </div>
        
        <div class="apps-grid">
            <!-- Email Blast App -->
            <a href="<?= base_url('email-blast') ?>" class="app-card">
                <div class="app-icon">📧</div>
                <div class="app-title">Email Blast</div>
                <div class="app-description">
                    Send personalized bulk emails to multiple recipients with professional formatting and tracking.
                </div>
                <ul class="app-features">
                    <li>Bulk email sending</li>
                    <li>Personalized messages</li>
                    <li>Professional HTML templates</li>
                    <li>Rate limiting protection</li>
                    <li>Custom reply-to addresses</li>
                </ul>
                <div style="text-align: center;">
                    <span class="app-status status-active">Active</span>
                </div>
            </a>
            
            <!-- Placeholder for future apps -->
            <div class="app-card" style="opacity: 0.7;">
                <div class="app-icon">📊</div>
                <div class="app-title">Analytics Dashboard</div>
                <div class="app-description">
                    Comprehensive analytics and reporting for your business metrics and performance tracking.
                </div>
                <ul class="app-features">
                    <li>Real-time analytics</li>
                    <li>Custom reports</li>
                    <li>Data visualization</li>
                    <li>Export capabilities</li>
                </ul>
                <div style="text-align: center;">
                    <span class="app-status status-coming-soon">Coming Soon</span>
                </div>
            </div>
            
            <div class="app-card" style="opacity: 0.7;">
                <div class="app-icon">👥</div>
                <div class="app-title">User Management</div>
                <div class="app-description">
                    Complete user management system with roles, permissions, and access control.
                </div>
                <ul class="app-features">
                    <li>User registration</li>
                    <li>Role-based access</li>
                    <li>Permission management</li>
                    <li>Activity tracking</li>
                </ul>
                <div style="text-align: center;">
                    <span class="app-status status-coming-soon">Coming Soon</span>
                </div>
            </div>
            
            <div class="app-card" style="opacity: 0.7;">
                <div class="app-icon">📁</div>
                <div class="app-title">File Manager</div>
                <div class="app-description">
                    Advanced file management system with upload, organization, and sharing capabilities.
                </div>
                <ul class="app-features">
                    <li>File upload/download</li>
                    <li>Folder organization</li>
                    <li>File sharing</li>
                    <li>Storage management</li>
                </ul>
                <div style="text-align: center;">
                    <span class="app-status status-coming-soon">Coming Soon</span>
                </div>
            </div>
        </div>
        
        <div class="system-info">
            <h3>System Information</h3>
            <div class="system-stats">
                <div class="stat-item">
                    <div class="stat-value">CodeIgniter <?= CodeIgniter\CodeIgniter::CI_VERSION ?></div>
                    <div class="stat-label">Framework Version</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?= ENVIRONMENT ?></div>
                    <div class="stat-label">Environment</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?= phpversion() ?></div>
                    <div class="stat-label">PHP Version</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">1</div>
                    <div class="stat-label">Active Apps</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; <?= date('Y') ?> SelMasta System. All rights reserved.</p>
            <p>Powered by CodeIgniter 4 Framework</p>
        </div>
    </div>
</body>
</html>
