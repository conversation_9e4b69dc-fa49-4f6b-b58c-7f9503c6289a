<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .stat-card.error {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }
        
        .stat-card.pending {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #4285f4;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3367d6;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .recent-emails {
            margin-top: 30px;
        }
        
        .recent-emails h3 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .email-list {
            list-style: none;
            padding: 0;
        }
        
        .email-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #4285f4;
        }
        
        .email-item.completed {
            border-left-color: #4CAF50;
        }
        
        .email-item.failed {
            border-left-color: #f44336;
        }
        
        .email-item.pending {
            border-left-color: #ff9800;
        }
        
        .email-subject {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .email-meta {
            font-size: 0.9em;
            color: #666;
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Email Sender Dashboard</h1>
        
        <?php if (session()->getFlashdata('success')): ?>
            <div class="alert alert-success">
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>
        
        <?php if (session()->getFlashdata('error')): ?>
            <div class="alert alert-error">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $stats['total_emails'] ?></div>
                <div class="stat-label">Total Emails</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number"><?= $stats['successful_emails'] ?></div>
                <div class="stat-label">Successful</div>
            </div>
            <div class="stat-card error">
                <div class="stat-number"><?= $stats['failed_emails'] ?></div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number"><?= $stats['pending_emails'] ?></div>
                <div class="stat-label">Pending</div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="<?= base_url('email-sender/new') ?>" class="btn btn-primary">
                ✉️ Compose New Email
            </a>
            <a href="<?= base_url('email-sender/history') ?>" class="btn btn-secondary">
                📋 View History
            </a>
        </div>
        
        <!-- Recent Emails -->
        <?php if (!empty($recent_emails)): ?>
        <div class="recent-emails">
            <h3>Recent Emails</h3>
            <ul class="email-list">
                <?php foreach ($recent_emails as $email): ?>
                <li class="email-item <?= $email['status'] ?>">
                    <div class="email-subject"><?= esc($email['subject']) ?></div>
                    <div class="email-meta">
                        <span class="status-badge status-<?= $email['status'] ?>"><?= ucfirst($email['status']) ?></span>
                        • Recipients: <?= $email['total_recipients'] ?>
                        • Sent: <?= date('M j, Y g:i A', strtotime($email['created_at'])) ?>
                        <?php if ($email['status'] === 'completed'): ?>
                            • Success: <?= $email['successful_sends'] ?>/<?= $email['total_recipients'] ?>
                        <?php endif; ?>
                    </div>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
