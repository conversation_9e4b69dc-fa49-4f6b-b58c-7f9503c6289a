<?php

namespace App\Models;

use CodeIgniter\Model;

class EmailLogModel extends Model
{
    protected $table            = 'email_logs';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'subject',
        'recipients',
        'message',
        'status',
        'sent_at',
        'error_message',
        'total_recipients',
        'successful_sends',
        'failed_sends'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'subject'    => 'required|min_length[3]|max_length[255]',
        'recipients' => 'required',
        'message'    => 'required|min_length[10]',
        'status'     => 'required|in_list[pending,sending,completed,failed]'
    ];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get email logs with pagination
     */
    public function getEmailLogs($limit = 10, $offset = 0)
    {
        return $this->orderBy('created_at', 'DESC')
                    ->limit($limit, $offset)
                    ->findAll();
    }

    /**
     * Get email statistics
     */
    public function getEmailStats()
    {
        $stats = [
            'total_emails' => $this->countAll(),
            'successful_emails' => $this->where('status', 'completed')->countAllResults(false),
            'failed_emails' => $this->where('status', 'failed')->countAllResults(false),
            'pending_emails' => $this->where('status', 'pending')->countAllResults(false)
        ];
        
        return $stats;
    }

    /**
     * Log email sending attempt
     */
    public function logEmailSend($data)
    {
        $logData = [
            'subject' => $data['subject'],
            'recipients' => is_array($data['recipients']) ? json_encode($data['recipients']) : $data['recipients'],
            'message' => $data['message'],
            'status' => $data['status'] ?? 'pending',
            'total_recipients' => $data['total_recipients'] ?? 0,
            'successful_sends' => $data['successful_sends'] ?? 0,
            'failed_sends' => $data['failed_sends'] ?? 0,
            'error_message' => $data['error_message'] ?? null,
            'sent_at' => $data['sent_at'] ?? date('Y-m-d H:i:s')
        ];

        return $this->insert($logData);
    }

    /**
     * Update email log status
     */
    public function updateEmailStatus($id, $status, $successCount = 0, $failCount = 0, $errorMessage = null)
    {
        $updateData = [
            'status' => $status,
            'successful_sends' => $successCount,
            'failed_sends' => $failCount
        ];

        if ($errorMessage) {
            $updateData['error_message'] = $errorMessage;
        }

        if ($status === 'completed' || $status === 'failed') {
            $updateData['sent_at'] = date('Y-m-d H:i:s');
        }

        return $this->update($id, $updateData);
    }
}
