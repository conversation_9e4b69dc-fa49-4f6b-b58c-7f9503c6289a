ERROR - 2025-06-15 05:04:53 --> <PERSON>rror connecting to the database: mysqli_sql_exception: Access denied for user ''@'localhost' (using password: NO) in C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', '', Object(SensitiveParameterValue), '', 3306, '', 0)
#1 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\codei46\system\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', NULL, false)
#4 C:\xampp\htdocs\codei46\system\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
#5 C:\xampp\htdocs\codei46\app\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', Array)
#6 C:\xampp\htdocs\codei46\app\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
#7 C:\xampp\htdocs\codei46\system\CodeIgniter.php(933): App\Controllers\EmailSender->index()
#8 C:\xampp\htdocs\codei46\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
#9 C:\xampp\htdocs\codei46\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\codei46\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\codei46\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\codei46\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Access denied for user ''@'localhost' (using password: NO) in C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\codei46\system\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', NULL, false)
#3 C:\xampp\htdocs\codei46\system\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
#4 C:\xampp\htdocs\codei46\app\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', Array)
#5 C:\xampp\htdocs\codei46\app\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
#6 C:\xampp\htdocs\codei46\system\CodeIgniter.php(933): App\Controllers\EmailSender->index()
#7 C:\xampp\htdocs\codei46\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
#8 C:\xampp\htdocs\codei46\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\codei46\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\codei46\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\codei46\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 {main}
CRITICAL - 2025-06-15 05:04:53 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Access denied for user ''@'localhost' (using password: NO)
[Method: GET, Route: email-sender]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows` FROM `email_logs`', null, false)
 3 SYSTEMPATH\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
 4 APPPATH\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', [])
 5 APPPATH\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\EmailSender->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
ERROR - 2025-06-15 05:06:09 --> Error connecting to the database: mysqli_sql_exception: Access denied for user ''@'localhost' (using password: NO) in C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', '', Object(SensitiveParameterValue), '', 3306, '', 0)
#1 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\codei46\system\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', NULL, false)
#4 C:\xampp\htdocs\codei46\system\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
#5 C:\xampp\htdocs\codei46\app\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', Array)
#6 C:\xampp\htdocs\codei46\app\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
#7 C:\xampp\htdocs\codei46\system\CodeIgniter.php(933): App\Controllers\EmailSender->index()
#8 C:\xampp\htdocs\codei46\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
#9 C:\xampp\htdocs\codei46\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\codei46\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\codei46\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\codei46\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Access denied for user ''@'localhost' (using password: NO) in C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\codei46\system\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', NULL, false)
#3 C:\xampp\htdocs\codei46\system\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
#4 C:\xampp\htdocs\codei46\app\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', Array)
#5 C:\xampp\htdocs\codei46\app\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
#6 C:\xampp\htdocs\codei46\system\CodeIgniter.php(933): App\Controllers\EmailSender->index()
#7 C:\xampp\htdocs\codei46\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
#8 C:\xampp\htdocs\codei46\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\codei46\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\codei46\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\codei46\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 {main}
CRITICAL - 2025-06-15 05:06:09 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Access denied for user ''@'localhost' (using password: NO)
[Method: GET, Route: email-sender]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows` FROM `email_logs`', null, false)
 3 SYSTEMPATH\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
 4 APPPATH\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', [])
 5 APPPATH\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\EmailSender->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
ERROR - 2025-06-15 05:06:34 --> Error connecting to the database: mysqli_sql_exception: Access denied for user ''@'localhost' (using password: NO) in C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php:201
Stack trace:
#0 C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php(201): mysqli->real_connect('localhost', '', Object(SensitiveParameterValue), '', 3306, '', 0)
#1 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#3 C:\xampp\htdocs\codei46\system\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', NULL, false)
#4 C:\xampp\htdocs\codei46\system\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
#5 C:\xampp\htdocs\codei46\app\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', Array)
#6 C:\xampp\htdocs\codei46\app\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
#7 C:\xampp\htdocs\codei46\system\CodeIgniter.php(933): App\Controllers\EmailSender->index()
#8 C:\xampp\htdocs\codei46\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
#9 C:\xampp\htdocs\codei46\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\codei46\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\codei46\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\codei46\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Access denied for user ''@'localhost' (using password: NO) in C:\xampp\htdocs\codei46\system\Database\MySQLi\Connection.php:246
Stack trace:
#0 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 C:\xampp\htdocs\codei46\system\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
#2 C:\xampp\htdocs\codei46\system\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*)...', NULL, false)
#3 C:\xampp\htdocs\codei46\system\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
#4 C:\xampp\htdocs\codei46\app\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', Array)
#5 C:\xampp\htdocs\codei46\app\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
#6 C:\xampp\htdocs\codei46\system\CodeIgniter.php(933): App\Controllers\EmailSender->index()
#7 C:\xampp\htdocs\codei46\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
#8 C:\xampp\htdocs\codei46\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 C:\xampp\htdocs\codei46\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#10 C:\xampp\htdocs\codei46\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 C:\xampp\htdocs\codei46\public\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 {main}
CRITICAL - 2025-06-15 05:06:34 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Access denied for user ''@'localhost' (using password: NO)
[Method: GET, Route: email-sender]
in SYSTEMPATH\Database\BaseConnection.php on line 467.
 1 SYSTEMPATH\Database\BaseConnection.php(620): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH\Database\BaseBuilder.php(1678): CodeIgniter\Database\BaseConnection->query('SELECT COUNT(*) AS `numrows` FROM `email_logs`', null, false)
 3 SYSTEMPATH\Model.php(928): CodeIgniter\Database\BaseBuilder->countAll()
 4 APPPATH\Models\EmailLogModel.php(72): CodeIgniter\Model->__call('countAll', [])
 5 APPPATH\Controllers\EmailSender.php(26): App\Models\EmailLogModel->getEmailStats()
 6 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\EmailSender->index()
 7 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
 8 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
DEBUG - 2025-06-15 05:09:07 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
DEBUG - 2025-06-15 05:09:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
CRITICAL - 2025-06-15 05:09:10 --> Error: Call to undefined function form_open()
[Method: GET, Route: email-sender/new]
in APPPATH\Views\email_sender\email_sender_create.php on line 189.
 1 SYSTEMPATH\View\View.php(224): include()
 2 SYSTEMPATH\View\View.php(227): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 3 SYSTEMPATH\Common.php(1173): CodeIgniter\View\View->render('email_sender/email_sender_create', [], true)
 4 APPPATH\Controllers\EmailSender.php(48): view('email_sender/email_sender_create', [...])
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\EmailSender->new()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\EmailSender))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
