<?php

namespace App\Controllers;

use CodeIgniter\Email\Email;

class EmailSender extends BaseController
{
    protected $email;

    public function __construct()
    {
        $this->email = \Config\Services::email();
    }

    /**
     * Display email sender dashboard
     */
    public function index()
    {
        // Mock stats since we're not using database
        $stats = [
            'total_emails' => 0,
            'successful_emails' => 0,
            'failed_emails' => 0,
            'pending_emails' => 0
        ];

        $data = [
            'title' => 'Email Sender Dashboard',
            'stats' => $stats,
            'recent_emails' => []
        ];

        return view('email_sender/email_sender_index', $data);
    }

    /**
     * Show email composition form
     */
    public function new()
    {
        $data = [
            'title' => 'Compose Email',
            'validation' => \Config\Services::validation()
        ];

        return view('email_sender/email_sender_create', $data);
    }

    /**
     * Process email sending
     */
    public function create()
    {
        $rules = [
            'subject' => 'required|min_length[3]|max_length[255]',
            'recipients' => 'required',
            'message' => 'required|min_length[10]',
            'smtp_host' => 'permit_empty|valid_url_strict',
            'smtp_user' => 'permit_empty|valid_email',
            'smtp_pass' => 'permit_empty',
            'smtp_port' => 'permit_empty|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $subject = $this->request->getPost('subject');
        $recipientsText = $this->request->getPost('recipients');
        $messageTemplate = $this->request->getPost('message');
        
        // Parse recipients
        $recipients = $this->parseRecipients($recipientsText);
        
        if (empty($recipients)) {
            return redirect()->back()->withInput()->with('error', 'Please enter valid recipients in the format: Name, <EMAIL>');
        }

        // Configure SMTP if provided
        $this->configureEmail();

        // Send emails (without database logging)
        $result = $this->sendBulkEmails($recipients, $subject, $messageTemplate);

        if ($result['success']) {
            return redirect()->to('/email-sender')->with('success', 
                "Emails sent successfully! {$result['successful']} sent, {$result['failed']} failed.");
        } else {
            return redirect()->back()->withInput()->with('error', 
                "Failed to send emails: " . $result['error']);
        }
    }

    /**
     * Show email sending history
     */
    public function history()
    {
        // Mock data since we're not using database
        $stats = [
            'total_emails' => 0,
            'successful_emails' => 0,
            'failed_emails' => 0,
            'pending_emails' => 0
        ];

        $data = [
            'title' => 'Email History',
            'emails' => [],
            'stats' => $stats
        ];

        return view('email_sender/email_sender_history', $data);
    }

    /**
     * Configure email settings
     */
    private function configureEmail()
    {
        $smtpHost = $this->request->getPost('smtp_host');
        $smtpUser = $this->request->getPost('smtp_user');
        $smtpPass = $this->request->getPost('smtp_pass');
        $smtpPort = $this->request->getPost('smtp_port');

        if (!empty($smtpHost) && !empty($smtpUser) && !empty($smtpPass)) {
            $config = [
                'protocol' => 'smtp',
                'SMTPHost' => $smtpHost,
                'SMTPUser' => $smtpUser,
                'SMTPPass' => $smtpPass,
                'SMTPPort' => $smtpPort ?: 587,
                'SMTPCrypto' => 'tls',
                'mailType' => 'html',
                'charset' => 'UTF-8',
                'wordWrap' => true
            ];

            $this->email->initialize($config);
        }
    }

    /**
     * Parse recipients from text input
     */
    private function parseRecipients($text)
    {
        $lines = explode("\n", trim($text));
        $recipients = [];

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            $parts = explode(',', $line);
            if (count($parts) >= 2) {
                $name = trim($parts[0]);
                $email = trim($parts[1]);

                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $recipients[] = ['name' => $name, 'email' => $email];
                }
            }
        }

        return $recipients;
    }

    /**
     * Send bulk emails with rate limiting
     */
    private function sendBulkEmails($recipients, $subject, $messageTemplate)
    {
        $successful = 0;
        $failed = 0;
        $errors = [];

        foreach ($recipients as $recipient) {
            try {
                $personalizedMessage = str_replace('{{name}}', $recipient['name'], $messageTemplate);

                $this->email->setFrom(config('Email')->fromEmail, config('Email')->fromName);
                $this->email->setTo($recipient['email'], $recipient['name']);
                $this->email->setSubject($subject);
                $this->email->setMessage($personalizedMessage);

                if ($this->email->send()) {
                    $successful++;
                } else {
                    $failed++;
                    $errors[] = "Failed to send to {$recipient['name']} ({$recipient['email']})";
                }

                $this->email->clear();

                // Rate limiting - wait 1 second between emails
                sleep(1);

            } catch (\Exception $e) {
                $failed++;
                $errors[] = "Error sending to {$recipient['name']}: " . $e->getMessage();
            }
        }

        return [
            'success' => $successful > 0,
            'successful' => $successful,
            'failed' => $failed,
            'error' => !empty($errors) ? implode('; ', array_slice($errors, 0, 3)) : null
        ];
    }
}
